{"translation": {"Login": "로그인", "FleetManagement": "플릿 관리", "FleetInfo": "플릿 정보", "MachineInfo": "장비 정보", "WorkOrder": "작업 지시", "Fleet": "플릿", "Search": "조회", "Delete": "삭제", "Add": "추가", "FleetName": "플릿명", "FleetNameN": "플릿명", "AddFleet": "플릿 추가", "Machine": "장비", "Driver": "운전자", "DriverCertified": "운전자 인증", "MachineList": "장비 목록", "ChangingSetting": "장비 설정 변경", "SettingHistory": "장비 설정 이력", "Model": "모델", "MachineID": "호기", "MachineIDN": "호기수", "SerialNo": "관리번호", "RefreshU": "갱신", "Country": "국가", "Location": "위치", "Select": "선택", "Total": "총", "Units": "대", "ID": "ID", "IDType": "ID 구분", "LicenseStartDate": "자격 시작일", "LicenseExpiryDate": "자격 만료일", "StartTime": "시작 시간", "EndTime": "종료 시간", "OperableEquipment": "작업 가능 장비", "Close": "닫기", "AddMachine": "장비 등록", "NoSearchResultsFound": "검색 결과가 없습니다.", "PleaseSelectTheEquipmentToConfigure": "설정하실 장비를 선택해 주세요.", "SelectEquipment": "장비 설정", "RegistrationEquipment": "등록 장비", "ClusterPassword": "클러스터 비밀번호", "OPSS": "OPSS", "HAC": "HAC", "AutoShift": "Auto Shift", "ZeroStart": "Zero Start", "DCSR": "DCSR", "SpeedLimit": "Speed Limit", "ShiftUp": "Shift Up", "ShiftDown": "Shift Down", "Kmh": "Km/h", "All": "전체", "Send": "전송", "Forwarding": "전송하기", "MessageSend": "메세지 전송", "TheConfigurationHasBeenUpdated": "설정 정보를 업데이트 하였습니다.", "TheConfiguredInformationHasBeenSentToTheRMCU": "설정된 정보로 RMCU에 전송하였습니다.", "ControlStatus": "제어상태", "LastUpdateE": "제어 전송 일시", "TransmissionDateTime2": "작업 전송 일시", "DateTime": "전송 일시", "ViewHistoryE": "전송 항목", "No": "No.", "Customer": "고객", "View": "보기", "SpecifyFleet": "플릿 지정", "MachineInformation": "장비 정보", "RMCU": "RMCU", "Item": "소모품 항목", "OperatingHour": "가동 시간", "DeliveryDate": "인도일", "WarrantyExpiryDate": "보증만료일", "DealerName": "딜러명", "Contact": "연락처", "ContactE": "연락처 입력", "ServiceContact": "서비스 연락처", "FaultAlarm": "고장 알림", "ConsumableAlarm": "소모품 알림", "LocationDeviationStatus": "위치이탈여부", "CommunicationStatus": "통신가능여부", "AddDriver": "운전자 추가", "EditDriver": "운전자 수정", "IDNumber": "ID 번호", "List": "목록", "PastHistory": "과거 지시 이력", "History": "히스토리", "RegisterWorkOrder": "작업 지시 등록", "Instructor": "지시자", "HimateAdmin": "Himate-admin", "Receiver": "수신자", "TargetEquipment": "대상 장비", "WorkDescription": "작업 내용", "Mileage": "Mileage", "Status": "현재 상태", "WorkEndTime": "작업 종료 시간", "IdlingH": "유휴 시간(h)", "Register": "등록", "TheWorkOrderDetailsHaveBeenSent": "작업 지시 내용을 전송하였습니다. ", "Date": "날짜", "Date2": "일시", "Completed": "완료", "ShiftStartTime": "교대 시작시간", "ShiftEndTime": "교대 종료시간", "Password": "비밀번호", "DriverPassword": "운전자 비밀번호", "Edit": "수정", "NotTransmitted": "미전송", "DriverRegistration": "운전자 등록", "FileUpload": "파일 업로드", "DownloadForms": "양식 다운로드", "CAllInDate": "파일 불러오기", "Activation": "활성화 여부", "Activation2": "개통", "ShiftTimeActivation": "교대시간 활성화 여부", "ShiftTime": "교대 시간", "SaveE": "수정", "ANewPieceOfEquipmentHasBeenRegistered": "신규 장비가 등록되었습니다.", "DriverManagement": "운전자 관리", "ManageDriver": "운전자 관리", "AllItineraryHistory": "전체 운행 이력", "ViewByEquipment": "장비로 보기", "ViewByDriver": "운전자로 보기", "DriverInformation": "운전자 정보", "Print": "출력", "CollisionManagement": "충격 관리", "ManageCollision2": "충격 관리", "ManageCollisionList": "목록 ", "ManageCollisionReport": "리포트 ", "RefCollision": "충격 기준", "RefAccident": "사고 기준", "RefOverSpeed": "과속 기준", "RefFrontRearCollision": "전후방 충격 기준", "RefSideCollision": "측면 충격 기준", "RefVerticalCollision": "상하 충격 기준", "RefFrontRearAccident": "전후방 사고 기준", "RefSideAccident": "측면 사고 기준", "RefVerticalAccident": "상하 사고 기준", "TheRecentCollisionCriteriaHaveBeenUpdatedPleaseTransmitTheData": "최근 충돌 정보가 변경된 항목입니다. 데이터 전송을 해주세요.", "OnlyNumbersBetween01And10CanBeEntered": "* 0.1 ~ 10 내 숫자만 입력 가능", "OnlyNumbersBetween1And30CanBeEntered": "* 1 ~ 30 내 숫자만 입력 가능", "Range110": "* 1 ~ 10 내 숫자만 입력 가능", "Range01250": "* 0.1 ~ 25.0 내 숫자만 입력 가능", "Range130": "* 1 ~ 30 내 숫자만 입력 가능", "TheEnteredValueIsLowerThanTheExistingOnePleaseEnterAValidNumber": "기존 수치보다 낮습니다. 다시 입력해 주세요.", "TheUpdatedInformationHasBeenSaved": "수정된 정보가 저장되었습니다.", "CollisionInformation": "충격 정보", "AccidentInformation": "사고 정보", "FrontRearCollision": "전후방 충격", "SideCollision": "측면 충격", "VerticalCollision": "상하 충격", "FrontRearAccident": "전후방 사고", "SideAccident": "측면 사고", "VerticalAccident": "상하 사고", "PreCheckManagement": "체크리스트 관리", "PreCheckCount": "체크리스트 수", "PreCheck": "체크리스트", "PreCheckInformationDetails": "체크리스트 상세 정보", "PreCheckRegistration": "체크리스트 등록", "PreCheckInformation": "체크리스트 정보", "Question": "질문", "Answer": "답변", "Critical": "심각도", "UnitLock": "장비 장금", "LastUpdate": "최근 수정 일자", "MachineCount": "장비 수", "DragAndDropAFileOrClick": "파일을 끌어다 놓거나 클릭", "ANewPreCheckHasBeenRegistered": "신규 체크리스트가 등록되었습니다.", "MachineManagement": "장비 관리", "HiMATEService": "FMS 서비스", "RentMachine": "렌트 장비", "UsedMachine": "중고 장비", "MCUReplace": "MCU 교체", "DescendingOrder": "내림차순", "AscendingOrder": "오름차순", "ServiceStartDate": "서비스 시작일", "ServiceEndDate": "서비스 종료일", "OK": "완료", "Check": "체크", "People": "명", "MachineType": "장비 종류", "Normal": "일반", "Test": "테스트", "MachinNo": "차대번호", "CheckMachine": "장비 체크", "ChassisModel": "영업모델", "RMCUCheck": "RMCU 체크", "Rent": "렌트", "DealershipCode": "대리점 코드", "IgnoreServiceStartConditions": "서비스 시작 조건 무시", "EMail": "이메일", "EMailAddress": "메일 주소", "Communication": "통신단말", "CardNo": "Card No.", "PINNo": "PIN No.", "PIN": "PIN", "Extend": "서비스 연장", "StartDate": "시작일", "EndDate": "종료일", "ExtendStartDate": "서비스 연장 시작일", "ExtendEndDate": "서비스 연장 종료일", "CustomerDeliveryDate": "고객 인도일", "EngineMarker": "엔진 제조사", "DesignModel": "설계모델", "SalesModel": "차대모델", "Save2": "등록", "InvalidValue": "유효하지 않는 값입니다.", "InvalidVINPleaseTryAgain": "유효하지 않은 차대번호 입니다. 다시 입력해 주세요.", "FaultManagement": "플릿 고장 관리", "CurrentFault": "발생 고장", "PastFault": "과거 고장", "Region": "지역", "Severity": "고장 심각도", "AlarmType": "알람 타입", "AlarmTypeEng": "Alarm Type", "SPN": "SPN", "FMI": "FMI", "AdvancedFilter": "상세필터", "Guide": "대응가이드", "ReceiptStatus": "접수 여부", "NotReceived": "미접수", "DateCriteria": "날짜 조건", "FaultOccurrenceDate": "고장 발생일", "FaultResolutionDate": "고장 소거일", "FaultResolutionDate2": "수리 완료일", "Fault": "<PERSON><PERSON>", "MapV": "지도보기", "ServiceHistory": "서비스 이력", "OperatingTime": "가동 시간", "ClaimInformation": "클레임 정보", "ClaimNumber": "클레임 번호", "ClaimType": "클레임 타입", "InspectionDetails": "점검 내용", "MaintenanceManagement": "플릿 소모품 관리", "State": "상태", "DateFaultOccurenceDate": "날짜(고장발생일)", "MaintenanceIntervalH": "교환 주기(h)", "RemainingTimeToNextMaintenanceH": "다음 교체까지(h)", "PlaceOrder": "발주", "Order": "발주하기", "Overdue": "초과", "Approaching": "임박", "Replacement": "교환", "FutureSupportPlanned": "추후 기능 지원 예정", "OutputReport": "리포트 출력", "MachineReport": "장비 리포트", "FleetReport": "플릿 리포트", "TotalMachine": "전체 장비(대수)", "ActiveMachine": "가동 장비(대수)", "WorkingDays": "장비 작업일(일)", "TotalDrivers": "소속 운전자 수", "OperationHoursHEngineRunWorkingTravelingIdling": "가동 시간(h) 엔진 운행 / 작업 / 주행 / 공회전", "ProductivityEfficiencyAnalysis": "생산성/효율성 통계", "MachineUtilizationRate": "장비 가동률", "AverageWorkingHours": "평균 근무 시간", "OperatinHours": "장비 이용률", "WorkingTraveling": "주행+작업시간", "WorkingO": "가동", "NonOperation": "비가동", "Repair": "수리", "WorkStartTime": "출근 시간", "QuittingTime": "퇴근 시간", "TotalWorkingTraveling": "총 주행 시간 + 작업 시간", "TotalIdling": "총 유휴 시간", "WorkingRankingByModel": "모델별 작업 시간 순위", "WorkingRankingByMachine": "장비별 작업 시간 순위", "PDFDownload": "PDF 다운로드", "Description": "설명", "DescriptionQuestion": "질문 내용", "BulkAssign": "일괄 지정", "OperationLimitaion": "가동제한", "ConfirmSet": "설정", "Hong": "홍길동", "ControlName": "제어명", "TransmissionStatus": "전송 상태", "Assign": "지정", "AssignS": "전송", "Dealership": "대리점", "Download": "다운로드", "Type": "타입", "Pieces": "개", "AccountManagement": "계정 관리", "Permission": "권한", "User": "사용자", "UserID": "사용자 ID", "PasswordReset": "비밀번호 초기화", "AddNew": "신규 등록", "Add2": "등록", "DealerCode": "딜러 코드", "DealerCode2": "대리점번호", "Machines": "장비수", "RegistrationDate": "등록일", "ServiceStatus": "서비스 현황", "ActionTakenBy": "조치자", "RegisterServiceHistory": "서비스 이력 등록", "MachineManagementStatusbyDealer": "대리점별 장비 관리 현황", "MachineManagementStatusbyDealerS": "대리점별\n장비 관리 현황", "NumberOfMachinesCustomerRegistrationRentalUsedAll": "장비수 고객등록 / 렌탈 / 중고 / 전체", "NumberOfRegisteredCustomers": "등록 고객 수", "DealerInformation": "대리점 정보", "TotalMachines": "전체 장비수", "CustomerRegistration": "고객 등록", "Used": "중고", "Rental": "렌탈", "MachineList2": "장비 리스트", "UserList": "사용자 리스트", "UserId": "사용자 ID", "UserName": "사용자명", "CumminsEngineInformation": "Cummins 엔진 정보", "Summary": "요약", "RemoteDiagnosis": "원격 진단", "ECDTargetMachine": "ECD 대상 장비", "ECDOnMachine": "ECD On 장비", "ECDOffMachine": "ECD Off 장비", "EngineStatus": "엔진 상태", "MachineWithECDSettingBeingChanged": "ECD 설정 변경 중인 장비", "CumulativeNumberOfReceivedMessagesPerMonth": "월간 누적 수신 메시지 개수", "CumulativeNumberOfReportsPerMonth": "월간 누적 리포트 개수", "MonthlyCumulativeOccurrenceFrequencyMaximumFaultCodeTop10": "월간 누적 발생 빈도 최대 Fault Code Top 10", "ECDONOFF": "ECD ON/OFF", "MailReceptionList": "메일 수신 리스트", "RecentRequestDateandTime": "최근 요청 일시", "VehicleDistance": "Vehicle Distance", "CalibrationIdentification": "Calibration Identification", "CalibrationVerificationNumber": "Calibration Verification Number", "NumberOfSoftwareIdentification": "Number of Software Identification", "EngineSerialNumberEng": "Engine Serial Number", "VIN": "VIN", "Make": "Make", "ModelEng": "Model", "ECDStatus": "ECD Status", "UnitNumberEng": "Unit Number", "ECDDetails": "ECD 상세 정보", "ECDInformation": "ECD 정보", "ECDActive": "ECD 활성화", "Request": "요청", "RecentResponseDate": "Recent Response Date(Korea)", "FCLimitationMessageDay": "F/C Limitation(message/day)", "HBLimitationMessageDay": "H/B Limitation(message/day)", "NoDataAvailable": "데이터가 없습니다.", "RevDate": "발생 일자", "CommType": "통신방식", "NumberOfOccurrences": "발생횟수", "MsgType": "Msg Type", "ECDRequest": "ECD 요청", "FaultListSendPayloadList": "Fault List & Send Payload List", "ReceivedMessage": "Received Message", "CumminsRecipentList": "Cummins Recipent List", "DateLocal": "Date (Local)", "CMISendResult": "CMI Send Result", "SendActive": "Send Active", "SendType": "Send Type", "NOTIFICATIONID": "NOTIFICATION_ID", "CustomerEquipmentID": "고객 장비 ID", "EngineSerialNumber": "엔진 시리얼번호", "VINOEMEquipmentID": "VIN / OEM Equipment ID", "EngineModel": "엔진 모델", "RecommendedAction": "권장 작업", "FaultCodeFCInformation": "고장 코드 정보", "FaultCode": "고장 코드", "Probability": "확률", "TimeStamp": "발생일시", "ExpectedCause": "예상 원인", "StartDate2": "서비스 시작일", "EndDate2": "서비스 종료일", "RemoteDiagnosisAnnouncement": "고장 코드 정보는 장비 성능이 제한되는 상황임을 나타내며, 다음과 같은 상황이 발생할 수 있습니다. 트러블슈팅 및 수리 가이드를 확인하고자 하시는 경우 QuickServe Online 을 참조하세요.  서비스 센터 정보 : 수리 관련 지원이 필요한 경우 커민스 서비스 센터  (************)으로 문의 해주세요.", "ElapsedTime": "Elapsed Time", "EngineOilPressureKPa": "Engine Oil Pressure(kPa)", "TransmissionOilTemperature": "Transmission Oil Temperature(℃)", "EngineFuelRateLh": "Engine Fuel Rate(L/h)", "BarometricPressureKPa": "Barometric Pressure(kPa)", "EngineSpeedRpm": "Engine Speed(rpm)", "EngineLoadatCurrentSpeed": "Engine Load at Current Speed(%)", "AirIntakePressureKPa": "Air Intake Pressure(kPa)", "BatteryVoltageV": "Battery Voltage(V)", "ActualTorque": "Actual Torque(%)", "DEFTank": "DEF Tank(%)", "Operationcondition": "Operation condition", "FuelLevel": "Fuel Level(%)", "IntakeManifold1Term": "Intake Manifold 1 Term(℃)", "CoolantTemperature": "Coolant Temperature(℃)", "FuelTemperature": "Fuel Temperature(℃)", "HydraulicOilTemperature": "Hydraulic Oil Temperature(°C)", "FaultCodeEng": "Fault Code", "DealerDetails": "대리점 상세 정보", "Recipient": "Recipient", "EmailEng": "E-mail", "ECDMailList": "ECD 메일 리스트", "DealerRecipientList": "Dealer Recipient List", "Report": "리포트", "ReportEng": "Report", "ConfirmPop": "확인", "FAQ": "FAQ", "Account": "계정", "SearchTC": "제목 또는 내용", "SearchByTitleContent": "제목 또는 내용", "ContentFAQ": "질문 내용을 입력해주세요.", "Title": "제목", "DateRes": "등록 일자", "FAQAdd": "FAQ 등록", "ReadPermission": "읽기 권한 ", "Language": "언어", "PinToTop": "상단 고정", "TemporaryStorage2": "임시저장 2", "QType": "질문 유형", "AreYouSureYouWantToDeleteTheFAQ": "FAQ를 삭제하시겠습니까?", "Cancel": "취소", "Save": "저장", "FAQDeleted": "FAQ가 삭제되었습니다.", "SaveTemporarily": "임시저장 되었습니다.", "NoFAQRegistered": "등록된 FAQ가 없습니다.", "NoResultsFound": "검색된 결과가 없습니다.", "QNA": "Q&A", "RegisterW": "작성자", "QNARes": "Q&A 등록", "TypeInquiry": "문의 유형", "Content": "내용", "AttatchFile": "첨부 파일", "DropFilesHereOrClickToUpload": "파일을 끌어다 놓거나 클릭", "UpTo5FilesCanBeUploadedJpgJpegBmpPngGif": "첨부 파일 없음 (jpg, jpeg, bmp, png, gif 파일 5개까지 업로드 가능합니다.)", "UpTo5FilesCanBeUploadedJpgJpegBmpPngGifPdf": "첨부 파일 없음 (jpg, jpeg, bmp, png, gif, pdf 파일 5개까지 업로드 가능합니다.)", "TitleThereAreUnsavedChangesIfYouLeaveWithoutSavingAnyUnsavedContentWillBeLost": "변경 또는 수정 중인 내용이 있습니다. 저장하지 않고 페이지를 벗어날 경우, 지금까지 작성된 내용이 사라집니다.", "Savedtemporarily": "임시저장 되었습니다.", "ReplyI": "문의 답변", "NoQNAregistered": "등록된 Q&A가 없습니다.", "MyInfo": "내 정보", "NotificationSettings": "알림 설정", "OptionalPersonalInformationCollectionAndUsage": "개인정보 선택 항목 수집 및 이용", "MobileNumber": "휴대폰 번호", "TimeSetting": "시간 설정", "City": "도시", "GMT": "GMT", "DateFormatUnitSettings": "날짜 형식 및 단위 설정", "Distance": "거리", "Volume": "용량", "Temperature": "온도", "Pressure": "압력", "Weight": "무게", "DomainSelection": "도메인 선택", "Km": "Km", "Mile": "mile", "L": "L", "Gal": "gal", "C": "˚C", "F": "˚F", "kgfCm": "kgf/cm²", "psi": "psi", "ton": "ton", "lb": "lb", "USTon": "US ton", "ChangePassword": "비밀번호 변경", "CurrentPassword": "현재 비밀번호 입력", "NewPassword": "새 비밀번호 입력", "ConfirmNewPassword": "새 비밀번호 확인", "EnterBetween10To16Characters": "10자 이상 ~ 16자 이내 입력", "MixTypesLettersNumbersAndSpecialCharacters": "영문 대문자, 소문자, 숫자, 특수문자 중 3종류 혼합", "NoConsecutive3CharactersOrNumbersInARowOnTheKeyboard": "3자리 연속된 키보드 상 문자, 숫자열 사용 불가능", "ChangeE": "확인", "PleaseEnterYourPassword": "비밀번호를 입력해 주세요.", "PasswordsMatch": "비밀번호가 일치합니다.", "PasswordsDoNotMatch": "비밀번호가 일치하지 않습니다.", "CollisionAlarm": "충격 알림", "OtherAlarm": "기타 알림", "ReceiveAlertsOnWeb": "웹에서 알림 받기", "ReceiveAlertsOnApp": "앱에서 알림 받기", "Fleet2": "소속 플릿", "Alarm": "알림", "EditC": "변경", "SaveC": "완료", "JustTheNumbers": "-제외하고 숫자만 입력", "DDMMYY": "일/월/년", "ContentQA": "문의 내용", "ContentReply": "답변 내용을 입력해주세요.", "Pending": "답변 대기", "CompletedQA": "답변 완료", "Deleted": "확인", "Notice": "공지사항", "NoticeAdd": "공지사항 등록", "AddNotice": "작성", "TypeNotice": "공지 유형", "RegisterNotice": "등록자", "ViewNum": "조회수", "Admin": "관리자", "TitleNotice": "제목을 입력해주세요.", "Previous": "이전글", "NextT": "다음글", "NoticePeriod": "게시 옵션", "Always": "상시 게시", "Period": "게시 예약", "ViewRead": "읽기 권한", "NoNoticeRegistered": "등록된 공지사항이 없습니다.", "TemporaryStorage": "임시 저장", "ThereAreUnsavedChanges": "변경 또는 수정 중인 내용이 있습니다.", "IfYouLeaveWithoutSaving": "저장하지 않고 페이지를 벗어날 경우,", "AnyUnsavedContentWillBeLost": "지금까지 작성된 내용이 사라집니다.", "ManualDownload": "매뉴얼 다운로드", "DriverManual": "운전자 메뉴얼", "ServiceManual": "서비스 메뉴얼", "TheManualProvidedForTheOperatorIncludes": "운전자를 위해 제공되는 매뉴얼로 사용 방법,", "UsageInstructionsSafetyWarningsAndMaintenanceInformation": "안전/주의사항, 유지보수 정보를 포함하고 있습니다.", "TheManualProvidedForTheOperatorIncludes2": "정비사나 기술자를 위해 제공되는 매뉴얼로, 정비,", "UsageInstructionsSafetywarningsAndMaintenanceInformation2": "유지보수, 문제해결을 위한 구체적인 절차와 기술정보를 제공합니다.", "WarningNotice": "주의사항", "ThisManualIsIntendedForTheSoleAndExclusiveUseOfYourselfAndContainsInformationThatIscCnfidential": "본 매뉴얼은 다운로드한 사용자 본인의 이용만을 목적으로 합니다.", "YouQAreHerebyNotifiedThatAnyUseiDsseminationDistributionOrcopyingOfTheManualOrTheInformationContainedThereinIsSTRICTLYPROHIBITED": "본 매뉴얼은 당사의 승인 없이 양도, 배포, 복사하는 것을 엄격하게 금지합니다.", "Approver": "승인자", "DateW": "작성 기준 일자", "StatusD": "문서상태", "TSG": "TSG", "TSGTitle": "TSG명", "Code": "Code", "Reason": "Reason", "Result": "Result", "StandardInformation": "기준 정보", "Circuit": "Circuit", "ComponentLocation": "Com-ponent Location", "StatusEng": "Status", "AddtionInformation": "추가정보", "TroubleShootingProcedure": "Trouble Shooting Procedure", "AdvancePreparation": "사전 준비 사항", "Step1": "Step 1", "CheckingWay": "확인 조건", "CheckW": "확인 방법", "Judgement": "판단", "YesNextProcedure": "Yes일 경우 다음 절차", "NoNextProcedure": "No일 경우 다음 절차", "TSGCreate": "TSG 생성", "TSGManage": "TSG 관리", "TSGBoard": "TSG 게시판", "ImageBox": "이미지박스", "ModelGroup": "모델 그룹명", "HCESPN": "(HCE)SPN", "HCESPNN": "HCESPN", "Remark": "수정일", "RemarkEng": "Remark", "RequestA": "승인 요청", "Level": "Level", "Dashboard": "대시보드", "Monitoring": "모니터링", "MachineDetails": "장비 상세", "Statistics": "통계", "Management": "관리", "MMC": "MMC", "MachineCommunication": "정비 알람 현황", "SubscriptionManagement": "구독관리", "Calendar": "캘린더", "ServiceInformation": "서비스 정보", "FaultMaintenanceAnalysis": "고장/소모품 통계", "FaultAnalysis": "고장 통계", "TotalAlarm": "총 고장 알림", "Cases": "건", "PendingAlarm": "대응", "ResponseAlarm": "대기", "CompletedAlarm": "완료", "MaintenanceAnalysis": "소모품 통계", "TotalR": "총 소모품 교체", "OverdueE": "교체 초과 장비", "Due": "교체 도래 장비", "CompletedE": "교체 완료 장비", "FaultAlarmRankingByModel": "모델별 고장알람 순위", "MaintenanceDueItemTrends": "소모품 도래 항목 추이", "Daily": "일간", "Weekly": "주간", "Monthly": "월간", "Operator": "운전자", "RankingByFaultSeverity": "고장 심각도별 순위", "MaintenanceOverdueItemTrends": "소모품 교체 지연률", "FaultResponseDelayRate": "고장 대응 지연률", "MaintenanceReplacementDelayRate": "소모품 교체 지연률", "ViewDetail": "자세히보기", "OperatingHours": "장비별 작업 시간 순위", "MonthlyWorkHours": "월별 작업 시간", "Idling": "유휴 시간", "TotalIdlingT": "총 주행 시간 + 작업 시간", "WorkingRankingByOperator": "작업자별 작업 시간 순위", "IdleTimeRankingByOperator": "작업자별 유휴 시간 순위", "EquipmentOperationAnalysisByOperator": "작업자별 장비 운영 통계", "TotalOperatingHours": "총 장비 운영 시간", "WorkingH": "작업 시간", "Traveling": "주행 시간", "WorkingTransitionAnalysis": "장비 가동 추이 분석", "CumulativeTime": "누적 시간", "DealerD": "대리점명", "Scope": "구분", "DateD": "일별", "MonthM": "월별", "Year": "연도별", "Refresh": "새로고침", "YearAverage": "연평균", "Average": "연평균", "Jan": "1월", "Feb": "2월", "Mar": "3월", "Apr": "4월", "May": "5월", "Jun": "6월", "Jul": "7월", "Aug": "8월", "Sep": "9월", "Oct": "10월", "Nov": "11월", "Dec": "12월", "Africa": "Africa", "China": "China", "India": "India", "Korea": "Korea", "LatinAmerica": "Latin America", "MiddleEast": "Middle East", "NorthAmerica": "North America", "Oceania": "Oceania", "CIS": "CIS", "Brazil": "Brazil", "Turkiye": "Turkiye", "Asia": "Asia", "Europe": "Europe", "WorkingHourAnalysis": "장비 가동 시간 분석", "Hours": "시간", "Minutes": "분", "EngineRunningTime": "엔진 가동 시간", "EngineRunningTimeAvg": "엔진 가동 시간", "RegionCountry": "지역 및 국가", "WorkingDaysAvg": "가동일", "PeriodD": "기간별", "Periodical": "기간별", "Days": "일수", "FuelEfficiency": "연비 통계", "FuelConsumption": "연료 소모량", "FuelConsumption2": "연료 통계", "FuelConsumptionAnalysis": "연료 소모량", "Month": "월", "EGRunHourH": "엔진 가동 시간(h)", "WorkingHourH": "작업 시간(h)", "TravelingHourH": "주행 시간(h)", "IdlingHourH": "공회전 시간(h)", "FuelRateLH": "연비(L/h)", "FuelUsedL": "연료 사용량(L)", "EstimatedFuelConsumption": "예상 연료 소모량", "EngineRunTT": "총 엔진 가동 시간", "ActualWorkingT": "총 작업 시간", "TravelingT": "총 주행 시간", "FuelLevelT": "총 연료 샤용량", "CAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated": "* 본 화면의 연료 사용량은 이론상 데이터로서, 사용환경이나 운전습관에 따라 실 사용량과는 다를 수 있습니다.", "BatteryAnalysis": "배터리 통계", "ChargingCountByModel": "모델별 충전 횟수", "BatteryConsumption": "배터리 소모량", "BatteryConsumptionAnalysis": "배터리 소모량", "LithiumBattery": "리튬 배터리", "LithiumBatteryInformation": "리튬배터리 정보", "EngineRunB": "총 엔진 가동 시간", "ActualWorkingB": "총 작업 시간", "TravelingB": "총 주행 시간", "BatteryUsedB": "총 배터리 샤용량", "TotalChangingCount": "총 충전 횟수", "AverageSOH": "SOH 평균", "ChargingCountByEquipment": "장비별 충전 횟수", "SOHDegradation": "SOH 감소량", "PackCurrent": "팩전류", "PackVoltage": "팩전압", "CollingWaterTemperature": "냉각수 온도", "BTMSToBattery": "(BTMS to Battery)", "BTMSConsumerPower": "BTMS 소비전력", "BatteryHighestTemperature": "배터리 최고 온도", "CoolingFAN": "쿨링팬 사용량", "AccumulatedChargingCapacity": "누적 충전량", "AccumulatedDischargingCapacity": "누적 방전량", "MaxCellVoltage": "셀 Max 전압", "MinCellVoltage": "셀 Min 전압", "MaxModuleTemperature": "모듈 Max 온도", "MinModuleTemperature": "모듈 Min 온도", "NumberOfCharges": "충전기 사용 횟수", "NumberOfOverDischarging": "과방전 사용 횟수", "NumberOfCycle": "배터리팩 Cycle", "WarningLightActivationList": "경고등 점등 목록", "TypeE": "에러 종류", "DeliveryDateE": "장비인도일", "WarrentyExpirationDate": "워런티 만료일", "ServerityE": "에러 심각도", "DateTimeE": "에러 발생 일시", "CollisionAnalysis": "충격 통계", "MachineCollisionAnalysis": "장비 충격 통계", "OperatorCollisionHistory": "운전자 충격 이력", "CollisionRankingByModel": "모델별 충격 순위", "CollisionRankingByEquipment": "장비별 충격 순위", "NumberOfCollision": "충격 횟수", "CollisionOccurrenceAnalysis": "충격 발생 통계", "DateO": "발생 일시", "FrontRear": "전후방 충격", "Side": "측면 충격", "Vertical": "상하 충격", "AddressC": "충격 주소", "LocationC": "충격 위치", "ConnectionAnalysis": "접속 통계", "ConnectionLog": "접속 로그", "PageConnectionAnalysis": "페이지 접속 통계", "Categories": "분류", "HoursH": "시간", "MinuteM": "분", "Times": "회", "IP": "IP", "TotalOperationMachines": "총 가동 시간", "TotalWorkingTime": "총 작업 시간", "TotalDrivingTime": "총 주행 시간", "TotalIdleTime": "총 유휴 시간", "TotalOperatingMachines": "총 운영 장비", "DailyWorkingMachines": "일간 작업 장비", "IdlePersonal": "유휴인원", "EcoIndex": "경제성", "ARank": "A등급", "BRank": "B등급", "CRank": "C등급", "DRank": "D등급", "NoReport": "보고없음", "UtilIndex": "활용도", "ErrorAlert": "고장코드", "PendingError": "대기 알람 코드", "ResponseError": "대응 알람 코드", "CompletedError": "완료 알람 코드", "MaintenanceAlert": "소모품 알람", "ReplacementDue": "교체 도래 장비", "ReplacementOverdue": "교체 초과 장비", "ReplacementCompleted": "교체 완료 장비", "ChargingCount": "충전 횟수", "BatteryUsage": "배터리 사용량", "EnergyEfficiency": "전비", "AverageEnergyEfficiency": "평균 전비", "FuelCost": "연료 비용", "AverageFuelEfficiency": "평균 연비", "FuelUnitPriceSetting": "연료 단가 입력하기", "MachineUtilizationRateA": "평균 장비 가동률", "NumberOfWorkers": "작업자 수", "AlarmF": "고장 알람", "TotalWorkingTimeO": "총 가동 시간 ", "LocationE": "장비 위치", "OperatingEfficiency": "운행 효율", "CollisionsPerMachine": "장비별 충격횟수", "CollisionsPerOperator": "운전자별 충격횟수 ", "NumberOfCollisions": "충격 횟수", "WeatherInformation": "기상 정보", "MachineName": "플릿명", "Weather": "날씨", "Warning": "경고", "FuelUnitPriceSettings": "연료 단가 입력", "FuelUnit": "연료 단위", "CurrencyUnit": "화폐 단위", "Cost": "비용", "SetTemperatureCriteria": "온도 기준 설정", "HighTemperature": "고온", "HighTemperatureSet": "고온 설정", "LowTemperature": "저온", "LowTemperatureSet": "저온 설정 ", "TemperatureUnit": "단위", "UnitsN": "대", "Won": "원", "ConfirmS": "저장", "VerticalN": "전후방", "SideN": "측면", "FR": "상하", "OtherC": "캘린더 표시", "DriverInfo": "운전자 정보", "EquipmentInfo": "장비 정보", "Event": "이벤트", "Etc": "기타", "Holidays": "공휴일", "AddC": "일정 추가", "AllDay": "하루종일", "AddSchedule": "일정 추가", "EditSchedule": "일정 편집", "TypeC": "구분 선택", "Equipment": "장비", "TitleC": "제목을 입력해주세요.", "ContentC": "내용을 입력해주세요.", "RepeatR": "반복 여부", "Start": "시작", "End": "종료", "TimeS": "시작 시간", "TimeE": "종료 시간", "N": "없음", "DAILY": "매일", "WEEKLY": "매주", "MONTHLY": "매월", "YEARLY": "매년", "EndDateCannotBeEarlierThanStartDate": "종료 날짜가 시작 날짜보다 빠릅니다.", "NotificationDetails": "일정 상세정보", "Repeat": "반복", "VisibilityStatus": "공개 여부", "ViewP": "공개 여부", "AllP": "공개", "RestrictedP": "일부 공개", "SelectCountry": "국가 선택 ", "YearAfter": "년간 ", "Public": "공개 ", "AddedNewSchedule": "신규 일정이 추가되었습니다.", "DeletingSchedule": "일정이 삭제되었습니다.", "Blue": "파란색", "Orange": "주황색", "Red": "빨간색", "Green": "초록색", "Purple": "보라색", "Gray": "회색", "CountryMultipleSelect": "국가 선택 (복수 선택 가능)", "WorkingHoursAnalysis": "장비 가동 시간 분석", "FuelAnalysis": "연료 통계", "Heatmap": "히트맵", "APIManagement": "API 관리", "AutonomousForklift": "무인지게차", "MessagesManagement": "메세지 관리", "ManagingPermissionGroupUsers": "권한 그룹 사용자 관리", "PartNumberLookup": "품번 조회", "DATADownload": "DATA 다운로드", "ItsNotYetAvailable": "아직 미지원입니다.", "MachineLocation": "장비 상세", "ReportE": "장비 통계", "Maintenance": "소모품", "DateRefresh": "정보 갱신", "DeliveryDateC": "고객인도일자", "ServiceDate": "서비스일자", "CommunicationStatusE": "통신 상태", "Available": "통신 가능", "OperatingStart": "운행 시작 시각", "OperatingEnd": "운행 종료 시각", "TotalOperatingHoursE": "총 운행 시간", "DailyWorkStatistics": "일간 작업 통계", "EngineOperationChart": "가동 차트", "DailyWorkStatisticsE": "엔진 가동 시간", "Top5OfEquipmentUilizationAmongSameModel": "동일 장비가동률 대비 상위 5%", "EngineRunT": "엔진 가동 시간", "EngineRun": "엔진 가동", "WorkingE": "작업 시간", "TravelingE": "주행시간", "IdlingE": "공회전", "EnginePowerMode": "엔진 파워 모드  ", "LastUpdated": "최근 업데이트", "Battery": "배터리", "MotorR": "모터(오른쪽)", "MotorL": "모터(왼쪽)", "Pump": "펌프", "EPS": "EPS", "Fuel": "연료", "HydraulicOil": "작동유", "EngineCoolant": "변속오일", "Transmission": "냉각수", "DEF": "요소수", "WorkingDay": "작업 일", "TotalEngineRunHour": "총 가동 시간", "EngineRunHourPerDayAvg": "일 평균 가동 시간", "DailyOperatingHours": "일별 가동 시간", "FuelConsumptionE": "연료사용량", "BatteryStatistics": "배터리 통계", "EstimatedChargingTimeByMachine": "장비별 충전 예상 시간", "AverageBatteryDepletionRateByHour": "배터리 시간별 평균 잔량 감소율", "BatteryDepletionRateComparedToSameModel": "동일 모델 대비 잔량 감소율", "DepletionRateperOperatingHour": "작업 시간당 감소율", "DepletionRateComparedtoOtherMachine": "타장비 대비 감소율", "SameModelAverage": "동일 모델 평균", "SelectedMachine": "선택 장비", "SameModelAverage2": "타장비", "TemperatureDistribution": "온도 분포도", "ADaysHours": "당일 시간", "TotalAverageHours": "전체 평균 시간", "OperatingHoursE": "가동 시간", "FuelUsedTotal": "총 연료사용량", "FuelRateTotalAvg": "평균 연비", "EqPeriodicalWarningText": "* 통신 상태 등의 이유로 장비의 가동내역이 수신되지 않은 기간이 있는 경우, 실제 가동 시간 및 연료 소비량 표시하는 합산보다 많을 수 있습니다.", "Details": "상세 정보", "TemperatureDistribution2": "온도 분포", "VersionInformation": "버전 정보", "MonitoringInformation": "모니터링 정보", "SendMessage": "메시지 전송", "SendMessageN": "메시지 전송", "SentReceivedMessage": "송수신 메시지", "SMSEMailTransmissionHistory": "SMS/E-Mail 전송 이력", "ASPhoneNumber": "A/S 전화번호", "ASSubDealer": "A/S 서브대리점", "RMSReceivedDataAnalysis": "RMS 수신데이터 분석", "ServiceHistoryInformation": "서비스 이력 정보", "CaseBook": "Case Book", "SendA": "알림 전송", "AlarmCodeList": "알람코드집", "AlarmMessageNotice": "알람 전송", "NotificationMethod": "수신방법 선택", "EMailSMS": "이메일 & SMS", "AppPush": "앱 푸시", "AlarmMessage": "알람 메시지", "RecipientC": "수신자 선택", "AddRecipient": "수신자 추가", "TheMessageHasBeenSent": "메세지가 전송되었습니다.", "Maker": "메이커", "DescriptionENG": "영문 설명", "DescriptionKOR": "한글 설명", "FaultCode2": "진단코드", "AlarmCodeCollection": "알람코드집", "TSGList": "TSG 리스트", "TroubleShootingCasebook": "Trouble Shooting Casebook", "File": "File", "AlarmHistory": "알람 이력", "CurrentFaultAlarm": "현재 고장 알람", "DTC": "DTC", "Source": "Source", "DTCHistory": "DTC 이력", "CI": "CI", "DTCDetails": "DTC 상세", "ElapsedTimeafterKeyOn": "Key On 후 경과 시간", "ElapsedTimeafterStarting": "시동 후 경과 시간", "EngineCoolantTemperature": "엔진 냉각수 온도", "HydraulicOilTemperatureK": "유압 오일 온도", "TransmissionOilTemperatureK": "변속기오일 온도", "EngineSpeed": "엔진 속도", "BatteryVoltage": "배터리 전압", "AlteratorVoltage": "발전기 전압", "VersionP": "프로그램 버전", "MaintenanceSchedule": "소모품 현황", "MaintenanceHistory": "소모품 이력", "MaintenanceIntervalHK": "교환주기(시간)", "DependingOnTheEquipmentSomeMaintenanceItemMayNotReflectReplacementHistoryFromTheMachine": "* 장비에 따라, 일부 유지보수 아이템은 장비로부터의 교체이력 반영이 불가능할 수 있습니다.", "ItemM": "소모품 항목", "MaintenanceInterval": "교환 주기", "Total2": "전체", "ReplaceDate": "교체일자", "HoursonItem": "Hours on Item", "RemainingTimetoNextMaintenance": "다음 교체까지", "TotalC": "교환", "ItemC": "소모품 항목", "ChangingReplacementCycle": "교환 주기 변경", "Cycle": "주기", "DoYouWantToRequestMaintenanceInformation": "소모품 정보를 요청하시겠습니까?", "DoYouWantToResetTheReplacementDays": "소모품 교체 일수를 리셋하시겠습니까?", "InformationHasBeenUpdated": "정보가 갱신되었습니다.", "ReplacementDaysHaveBeenReset": "교체 일수가 리셋되었습니다.", "MachineTravel": "이동경로 조회", "Latitude": "위도", "Longitude": "경도", "GeoFencing": "지오펜싱 설정", "StatusE": "경계지역 설정 상태", "SettingTimeE": "경계지역 설정 시간", "BasePositionLatLongAlt": "경계지역 기준 위치", "LatestLocationReceivedDate": "최근 위치 수신 시간", "LatestLocationName": "최근 위치(지명)", "LocalHour": "현지시간", "TimeToReceiveDailyReport": "정보 갱신 주기", "Engage": "Engage", "Release": "Release", "SpeedLimitK": "제한속도", "StartRestriction": "시동제한", "RMCUUpdate": "RMCU 업데이트", "Geofencing": "지오펜싱", "Update": "업데이트", "LastUpdate2": "최근 업데이트일", "InformationHasBeenRequested": "정보가 요청되었습니다.", "PasswordHasNeenChanged": "비밀번호가 변경되었습니다.", "ChangeClusterPassword": "클러스터 비밀번호 변경", "PleaseEnter5DigitNumber": "비밀번호는 숫자 5자리로 구성해 주세요. ", "CurrentPassword2": "현재 비밀번호", "NewPassword2": "새 비밀번호", "VerifyPassword": "새 비밀번호 확인", "PasswordE": "비밀번호 입력", "PasswordIncorrect": "비밀번호가 일치하지 않습니다.", "GeoFencingSetting": "지오펜싱 설정", "RangeSetting": "가동 범위 설정", "GeofencingOFF": "지오펜싱 설정 안 함", "Radius": "반경 설정", "Default": "<PERSON><PERSON><PERSON> ", "TotalSum": "총 합계", "AveragePerDay": "일 평균", "WorkingR": "실제작업", "FuelLevel2": "연료 레벨", "FuelUsedECM": "연료사용량(ECM)", "FuelUsedTank": "연료사용량(Tank)", "WCAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated": "* 주의: 본 화면의 연료 사용량은 이론상 데이터로서, 사용환경이나 운전습관에 따라 실 사용량과는 다를 수 있습니다.", "KeyOn": "Key on", "Working": "작업", "BatteryLevel": "배터리 레벨", "HydraulicOilMaximum": "작동유 최고 온도", "CoolantMaximum": "Coolant (Maximum)", "TransmissionOilMaximum": "Transmission Oil (Maximum)", "MaxTemperatureRecordHistory": "최고 온도 갱신 이력", "LastYearValueBasedOnSearchDate": "* 검색 일자 기준, 최근 1년 데이터만 표시됩니다.", "MaxCoolantTemperature": "냉각수 최고온도", "MaxTransmissionOilTemperature": "변속오일 최고온도", "TransmissionFormat": "전송 형식", "NetworkS": "Network 전송", "SMSS": "SMS 전송", "SMS": "SMS", "TemperatureRequestHasBeenCompleted": "온도 요청이 완료되었습니다.", "MotorRMMaximum": "Motor RM 최고 온도", "MotorLMMaximum": "Motor LM 최고 온도", "MotorPumpMaximum": "Motor Pump 최고 온도", "MotorEPSMaximum": "Motor EPS 최고 온도", "MotorRM": "Motor(RM)", "MotorLM": "Motor(LM)", "MotorPump": "Motor(Pump)", "MotorEPS": "Motor(EPS)", "MaxMotorRMTemperature": "Motor(RM) 최고온도", "MaxMotorLMTemperature": "Motor(LM) 최고온도", "MaxMotorPumpTemperature": "Motor(Pump) 최고온도", "MaxMotorEPSTemperature": "Motor(EPS) 최고온도", "Controller": "제어기", "InformationM": "MCU 정보", "ChagenHistoryM": "MCU 교체 이력 조회", "ManufacturingDate": "제조일자", "ReceptionDate": "수신일자", "ChangeDate": "교체 일자", "MileageHour": "차이 Mileage / 시간", "MileageSecond": "차이 Mileage / 초", "InputBy": "입력자", "InputDate": "입력일자", "WouldYouLikeToRequestTheRMCUBD": "RMCU BD를 요청하시겠습니까?", "RMCUBDRequestIsComplete": "RMCU BD 요청이 완료되었습니다.", "MCUInformationRequestIsComplete": "MCU 정보 요청이 완료되었습니다.", "Manufacturer": "생산자", "SerialNoK": "시리얼번호", "PriviousSerialNo": "이전 시리얼번호", "NewSerialNo": "신규 시리얼번호", "Remarks": "비고", "InformationR": "RMCU 제어기 정보", "MCUI": "MCU 제어기 정보", "HistoryC": "제어기 이력", "Manufacturer2": "제조사", "DateS": "수신 일자", "ASNumber": "A/S 전화번호", "EquipmentMonitorCluster": "장비 모니터(클러스터)", "ASDealer": "A/S 대리점", "PleaseEnterThePhoneNumber": "휴대폰 번호를 입력해주세요.", "ProductionI": "생산 정보", "Chassis": "차대 모델", "DesignM": "설계 모델", "ProductionStart": "생산착수일", "Shipping": "출하일", "CustomerDelivery": "고객인도일", "EquipmentNo": "Equipment No.", "EngineNo": "Engine No.", "CustomerN": "고객상호", "Sales": "영업 정보", "ContactNo": "계약번호", "CustomerName": "고객명", "Amount": "금액", "PaymentTerms": "지불조건", "RequestedDelivery": "납기요청일자", "Optional": "선택 사양 정보             ", "CodeNo": "Code No.", "CodeK": "코드", "CodeName": "코드명", "Spec1Spec2": "규격 1 / 규격 2", "Weight1Weight2": "중량1 / 중량2", "PhoneNumber": "핸드폰 번호", "Phone": "핸드폰", "Name": "이름", "NameE": "이름 입력", "MCU": "MCU", "RequestM": "MCU 정보 요청", "RequestR": "RMCU BD 요청", "InformationBR": "RMCU 기본 정보", "ChangeHistoryR": "RMCU 교체 이력 조회", "TimeR": "조회 시간", "WattingT": "대기시간", "BatteryUsed": "배터리 사용량", "DateC": "충격 일자", "DateR": "수신일", "MaxHydraulicOilTemperature": "작동유 최고온도", "EngineOperatingHistory": "엔진 가동 이력", "WorkingT": "작업시간", "FuelLevelR": "연료잔량", "FuelUsedPerDayAvg": "일 평균 연료사용량", "Setting2": "설정값", "ResetKE": "리셋", "DateI": "조회 기간", "Setting": "설정", "InsideTheDesignatedArea": "지정된 지역 안에 있습니다.", "FaultLocation": "고장 부위", "FaultSeverity": "고장 심각도", "ConsumableReplacementLevel": "소모품 교체 레벨", "Warning2": "주의", "ServiceSoon": "서비스 필요", "ServiceNow": "서비스 즉시 필요", "StopSoon": "정지 필요", "StopNow": "즉시 정지 필요", "DueDate": "교체기한 도래", "OverDue": "교체기한 초과", "DealerM": "관할 대리점", "ShipmentDate": "장비 출고일", "WarrantyPeriod": "워런티 종료여부/종료일", "AlarmI": "진행 중 알람", "DateTimeF": "고장 일시", "DescriptionF": "고장 내용", "Submit": "접수하기", "SetWatchlistEquipment": "주시 장비 설정", "DoYouWantToSetThisEquipmentAsWatchlistEquipmentTheSelectedEquipmentCanBeViewedInTheStatistics": "운전자 & 장비 삭제해당 장비를 주시 장비로 설정하시겠습니까? 설정된 장비는 통계에서 확인할 수 있습니다.", "AreYouSureYouWantToDeleteTheSelectedRegionDeletedInformationCannotBeRecovered": "운전자 & 장비 삭제선택된 지역을 삭제하시겠습니까? 삭제한 정보는 복구할 수 없습니다.", "AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheRegion": "선택된 장비를 지역에서 삭제하시겠습니까?", "AreYouSureYouWantToRemoveTheSelectedDriversFromTheCountry": "운전자 & 장비 삭제 선택된 운전자를 국가에서 삭제하시겠습니까?", "TheEquipmentHasBeenSetAsWatchlistEquipment": "주시장비가 설정되었습니다.", "TheRegionHasBeenDeleted": "지역이 삭제되었습니다.", "TheEquipmentHasBeenRemoved": "장비가 삭제되었습니다.", "AddRegion": "지역 추가", "RegionName": "지역명", "RegionNameE": "지역명 입력", "RegionCode": "지역코드", "RegionCodeE": "지역코드 입력", "ANewRegionHasBeenAdded": "신규 지역이 추가되었습니다.", "AlarmC": "알람 내용", "SeverityF": "고장 심각도", "ANewMachineHasBeenRegistered": "신규 장비가 등록되었습니다.", "NoSeachResultsFound": "검색 결과가 없습니다.", "ShipmentDateE": "장비 출고일", "FaultInfo": "고장 정보", "DateTime2": "접수일시", "Symptoms": "증상", "SymptomsT": "증상을 입력해주세요.", "ResponseMethod": "대응방법", "ResponseMethodT": "대응방법을 입력해주세요.", "ResponseCompletionStatus": "대응완료 여부", "Yes": "Yes", "PleaseSetthecountry": "국가를 설정해 주세요.", "SubmissionHistory": "접수 이력", "Affiliation": "소속", "AffiliationE": "소속 입력", "ModifiedDateTime": "최종수정일시", "Edit2": "편집", "AddContact": "연락처 추가", "ContactInformation": "연락처 추가", "ANewContactHasBeenEegistered": "신규 연락처가 등록되었습니다.", "SubmissionHistoryM": "MMC 접수 이력", "OccurenceDateTime": "고장 발생 일시", "SubmissionDateTime": "고장 접수 일시", "Method": "대응 방법", "GuideM": "대응 가이드", "StatusM": "완료 여부", "Submitter": "접수자명", "RegisterSubmission": "접수 등록", "Reset": "초기화", "FaultK": "고장", "Maint": "정비 중", "Map": "지도", "DetailsV": "상세보기", "MechcanicalSafetyDevicesHarnessSeatBeltDataAndWarningLabelsLegibleSafe": "Mechcanical Safety Devices- Harness,Seat Belt, Data and Warning Labels- Legible & Safe", "Recently": "최근수신일", "MachineStatus": "장비 상태", "Idle": "유휴", "InOperation": "운행 중", "MaintenanceItem": "소모품 교체", "NoGPSSignal": "GPS 신호 없음", "ViewFilters": "상세 필터 보기", "HideFilters": "상세 필터 접기", "Chatbot": "챗봇", "PartsRecommendation": "부품 추천", "MalfunctionResponseGuide": "고장 대응 가이드", "Notification": "공지사항", "GoHome": "홈으로 이동", "Korean": "한국어", "IDE": "아이디 입력", "RememberMe": "아이디 저장", "Join": "회원가입", "FindID": "아이디 찾기", "ResetPassword": "비밀번호 재설정", "UserAgreementPrivacyPolicy": "이용약관 및 개인정보처리방침", "Next": "다음", "Logout": "로그아웃", "English": "English", "FindByEMailAddress": "이메일 주소", "FindByPhoneNumber": "전화번호", "EMailE": "Enter E-Mail", "SelectDomain": "도메인", "EnterPhoneNumberNoDashes": "- 제외하고 전화번호 입력", "EMailN": "이메일 주소", "SendVerificationCode": "인증번호 발송", "EnterVerificationCode": "인증번호 입력", "Verify": "인증하기", "YourIDHasBeenFound": "아이디 찾기가 완료되었습니다.", "EnterBetween8To12Characters": "* 10자 이상 ~ 16자 이내 입력", "SelectOutputFileType": "출력", "ExcelFileFormatIfYouHaveMSExcel": "엑셀 파일 형식(MS Excel 설치 시)", "ExcelFileFormatIfYouDontHaveMSExcel": "CSV 파일 형식(MS Excel 미설치 시)", "OKS": "출력", "DetailE": "상세 정보", "day": "일", "FromYesterday": "전일 대비", "Up": "증가", "Down": "감소", "SelectedMachineF": "선택된 장비", "RegionA": "주소", "Elication": "해발", "PasswordMustBeAtLeast6CharactersLong": "비밀번호는 최소 6자 이상이어야 합니다.", "FaultE": "장비고장", "RefFrontRear8GRefSide8GRefVertical8G": "전후방 기준: 8G 측면 기준: 8G 상하 기준: 8G", "DealerNameD": "딜러명(대리점명)", "LoactionE": "장비위치", "Replace": "대치", "TheFleetHasBeenDeleted": "플릿이 삭제되었습니다.", "FailedToDeleteFleet": "플릿 삭제에 실패하였습니다.", "ANewFleetHasBeenAdded": "신규 플릿이 추가되었습니다.", "AreYouSureYouWantToDeleteTheFleetDeletedDataCannotBeRecovered": "플릿을 삭제하시겠습니까?\n삭제된 정보는 복구할 수 없습니다.", "NoFleetAvailableToAssignWouldYouLikeToAddAFleet": "지정할 플릿이 없습니다.\n플릿 추가하시겠습니까?", "TheSelectedFleetAlreadyHasAssignedEquipmentDoYouWantToContinue": "선택된 플릿에 이미 저장된 장비가 있습니다.\n계속하시겠습니까?", "TheDriverHasBeenRemoved": "운전자가 삭제되었습니다.", "AreYouSureYouWantToRemoveTheSelectedDriverFromTheEquipment": "선택된 운전자를 장비에서\n삭제하시겠습니까?", "TheSelectedInformationHasBeenDeleted": "장비 삭제 선택된 장비를 운전자에서\n삭제하시겠습니까?", "TheSelectedInformationHasBeenDeleted2": "선택된 정보가 삭제되었습니다.", "AreYouSureYouWantToDeleteTheSelectedDriversOrEquipmentDeletedInformationCannotBeRecovered": "선택된 운전자 또는 장비를 삭제하시겠습니까?\n삭제된 정보는 복구할 수 없습니다.", "TheDataHasBeenTransmitted": "데이터가 전송되었습니다.", "TheEquipmentHasBeenDeleted": "장비가 삭제되었습니다.", "AreYouSureYouWantToDeleteTheSelectedPreCheck": "선택된 체크리스트를\n삭제하시겠습니까?", "ThePreCheckHasBeenDeleted": "체크리스트가 삭제되었습니다.", "AreYouSureYouWantToDeleteTheDeviceTheServiceHistroy": "서비스 이력을 삭제하시겠습니까?", "TheServiceHistoryHasBeenDeleted": "서비스 이력이 삭제되었습니다.", "AreYouSureYouWantToDeleteTheNotice": "공지사항을 삭제하시겠습니까?", "NoticeDeleted": "공지사항이 삭제되었습니다.", "AreYouSureYouWantToDeleteOnceDeletedItCannotBeRecovered": "삭제하면 복구할 수 없습니다.\n삭제하시겠습니까?", "DoYouWantToLeaveThisPage": "페이지를 나가기", "ThereAreUnsavedChangesIfYouLeaveWithoutSavingAnyUnsavedContentWillBeLost": "변경 또는 수정 중인 내용이 있습니다.\n저장하지 않고 페이지를 벗어날 경우,\n지금까지 작성된 내용이 사라집니다.", "AreYouSureYouWantToDeleteTheCrashDetectionSettingsFromTheSelectedEquipmentDeletedInformationCannotBeRecovered": "선택된 장비에서 충돌 감지 설정을 삭제하시겠습니까? 삭제된 정보는 복구할 수 없습니다.", "HowToUse": "이용방법", "Others": "기타", "Campaign": "캠페인", "RFID": "RFID", "Dealer": "딜러", "SubUser": "서브사용자", "FOC": "FOC", "ServicePeriod": "서비스 기간", "Data": "데이터", "HW": "H/W", "SW": "S/W", "SuperAdministrator": "최고 관리자", "DelearAdministrator": "딜러 관리자", "RepairAdministrator": "수리 책임자", "MMXAdministrator": "MMX 관리자", "Actvice": "Actvice", "InActive": "InActive", "ECD": "ECD", "MCD": "MCD", "MMDDYY": "월/일/년", "YYMMDD": "년/월/일", "Present": "현재", "1DaysAgo": "1일 전", "2DaysAgo": "2일 전", "3DaysAgo": "3일 전", "4DaysAgo": "4일 전", "5DaysAgo": "5일 전", "DateRefreshT": "온도 요청", "DateRefreshC": "충격 관리", "Receipt": "접수", "MMCStatistics": "MMC 통계", "FailureAlarmOccurrenceRateByPeriod": "기간별 고장 알람 발생 건수", "ConsumablesAlarmOccurrenceRateByPeriod": "기간별 소모품 알람 발생 건수", "TotalFaultCodes": "총 고장 코드 수", "GazeEquipmentList": "주시 장비 목록", "ResponsePerformance": "대응 성과", "Manager": "담당자", "WarrantyPeriodD": "보증기간", "MySetting": "개인 설정", "MyMachine": "개인 장비", "AlarmIng": "진행 중 알람", "LatestEquipmentLocation": "최근 수신된 장비 위치 정보 기준", "CurrentEquipmentLocation": "현재 장비 위치 기준", "ConfirmSetting": "설정하기", "Change": "변경", "PostingTime": "게시 시간", "MPG": "연비", "OperatingHoursET": "엔진 가동 시간", "TypeS": "구분", "ItemM2": "소모품", "UpadateCompleted": "업데이트 완료", "ServicePhoneNumber": "A/S 전화번호", "DealerAS": "A/S 대리점", "SubDealerAS": "A/S 서브대리점", "FleetInfomation": "플릿 정보", "UnitsV": "대", "AddD": "운전자 등록", "TypeN": "공지 유형", "SystemCheck": "시스템 점검", "TermsChanges": "약관 변경", "BatchAssignment": "일괄 지정", "Load": "불러오기", "Today": "오늘", "Week": "이번 주", "MonthT": "이번 달", "HDHYUNDAIXITESOLUTION": "(주)현대사이트솔루션", "Terms": "이용약관", "Conditions": "개인정보처리방침", "BatteryOperatingHour": "배터리 작동 시간", "TheFleetHasBeenAssigned": "플릿에 지정되었습니다.", "ANewDriverHasBeenRegistered": "신규 운전자가 등록되었습니다.", "TheModifiedInformationHasBeenSaved": "수정된 정보가 저장되었습니다.", "ModelSerialNoMachineID3": "모델 / 관리번호 / 호기", "LastRecievedDate": "최근 수신일", "Reply": "답변", "SouthKorea": "대한민국", "82SouthKorea": "+82(대한민국)", "Favorites": "즐겨찾기", "RemoveFavorite": "즐겨찾기 삭제", "ViewDetails": "상세보기", "EXCHANGE": "교환", "IMMINENT": "임박", "EXCESS": "초과", "CasesA": "알림 건수", "Response": "대응", "EditContact": "연락처 수정", "AddS": "저장", "AddToFavoritesT": "즐겨찾기를 추가해 주세요.", "PleaseSetTheCountry": "국가를 설정해 주세요.", "AddToFavorites": "즐겨찾기 추가", "FavoriteName": "즐겨찾기명", "EnterFavoriteName": "즐겨찾기명 입력", "NewFavoriteAdded": "새 즐겨찾기가 추가되었습니다.", "SelectMachine": "장비 선택", "DealerDS": "대리점", "AreYouSureYouWantToDeleteTheSelectedContactFromTheCountry": "선택된 연락처를 국가에서 삭제하시겠습니까?", "AccumulatedTime": "누적 시간", "AreYouSureYouWantToRemoveThisFavoriteTheMachineInformationWillNotBeDeleted": "해당 즐겨찾기를 삭제하시겠습니까?\n장비 정보는 삭제되지 않습니다.", "TheFavoriteHasBeenRemoved": "즐겨찾기가 삭제되었습니다.", "AreYouSureYouWantToDeleteTheSelectedEquipmentFromYourFavorites": "선택된 장비를 즐겨찾기에서 삭제하시겠습니까?", "TheMachineHasBeenRemoved": "장비가 삭제되었습니다.", "TheContactHasBeenDeleted": "연락처가 삭제되었습니다.", "WorkingInformation": "작업정보", "EngineRunHourTotal": "총 가동시간", "TimeO": "가동 시간", "Day": "일", "FrontRearC": "X축 충격 기준", "SideC": "Y축 충격 기준", "TopBottomC": "Z축 충격 기준", "SpeedC": "사고 기준", "AccidentC": "과속 기준", "Reset2": "요청", "RegionS": "지역별", "CountryS": "국가별", "ModelS": "모델별", "AreYouSureYouWantToDeleteTheSelectedEquipmentFromTheFleet": "선택된 장비를 플랫에서 삭제하시겠습니까?", "TheCrashDetectionSettingsHaveBeenDeleted": "선택된 장비를 플랫에서 삭제하시겠습니까?", "TheCollisionDetectionSettingHasBeenDeleted": "충돌 감지 설정이 삭제되었습니다.", "TheFleetNameIsAlreadyInUse": "이미 사용중인 플릿명입니다.", "NoticeBadge": "공지", "CustomerRentalUsedTotal": "고객등록/렌탈/중고/전체", "TotalEquipmentOperations": "총 장비 작업", "PleaseEnterValidEmailAddress": "유효한 이메일 주소를 입력하세요.", "AreYouSureYouWantToDeleteTheQA": "Q&A를 삭제하시겠습니까?", "QADeleted": "Q&A가 삭제되었습니다.", "RefFrontRear": "전후방 기준", "RefSide": "측면 기준", "RefVertical": "상하 기준", "Sources": "출처", "InService": "서비스 중", "OutOfService": "종료", "More": "더보기", "HoursS": "시", "MaintenanceReachedItemTrends": "소모품 초과 항목 추이", "FleetAll": "플릿 전체", "NoticeEdit": "공지사항 수정", "ThereIsNoPreviousArticle": "이전 글이 없습니다.", "ThereIsNoNextArticle": "다음 글이 없습니다.", "DepletionRateComparedToOthers": "Depletion Rate Compared to Others", "InProgress": "In Progress", "Resolved": "Resolved", "Expired": "Expired", "Reached": "Reached", "MaintenanceExpiredItemTrends": "Maintenance Expired <PERSON><PERSON> Trends", "NonOperationS": "Non-Operation", "OperationTrend": "Operation Trend", "OperationHour": "Operation Hour", "AccessLog": "Access Log", "GalH": "gal/h", "Vehicle": "Vehicle", "Truck": "Truck", "HeavyEquip": "Heavy Equip.", "Agricultural": "aaAgriculturala", "Drone": "Drone", "ForgotUsername": "Forgot username", "PrivacyPolicy": "Privacy Policy", "TermsOfUse": "Terms of Use", "DontHaveAnAccount": "Don't have an account?", "OPTIMIZINGTRANSPORTATIONFLEETMANAGEMENTFORSMARTEROPERATIONS": "OPTIMIZING TRANSPORTATION & FLEET MANAGEMENT FOR SMARTER OPERATIONS", "ProfileManagement": "Profile Management", "PersonalSetting": "Personal Setting", "PersonalInformation": "Personal Information", "AccountInformation": "Account Information", "UserPermission": "User Permission", "TimeSettings": "Time Settings", "TimeZoneUTC": "Time Zone (UTC)", "FormatUnitSettings": "Format & Unit Settings", "DateFormat": "Date Format", "DistanceUnit": "Distance Unit", "VolumeUnit": "Volume Unit", "PressureUnit": "Pressure Unit", "WeightUnit": "Weight Unit", "Done": "Done", "VsPerviousDay": "vs Pervious day", "TotalOperatingVehicle": "Total Operating Vehicle", "TotalVehicleOperations": "Total Vehicle Operations", "MaintenanceReminder": "Maintenance Reminder", "DueSoon": "Due Soon", "Replaced": "Replaced", "RefuelingCount": "Refueling Count", "AvgVehicleUtilization": "Avg. Vehicle Utilization", "DrivingEfficiency": "Driving Efficiency", "VehicleImpactCount": "Vehicle Impact Count", "DriverImpactCount": "Driver Impact Count", "FrontRearImpact": "Front/Rear Impact", "SideImpact": "Side Impact", "VerticalImpact": "Vertical Impact", "EquipmentName": "Equipment Name", "TemperatureThresholdSetting": "Temperature Threshold Setting", "Alert": "<PERSON><PERSON>", "TemperatureThresholdSettings": "Temperature Threshold Settings", "H": "시간", "VehicleNumber": "Vehicle Number", "AllFleets": "All Fleets", "EquipmentNumber": "Equipment Number", "AssetID": "Asset ID", "Filter": "Filter", "EquipmentStatus": "Equipment Status", "Received": "Received", "EquipmentStatusMap": "Equipment Status Map", "TrackingMap": "Tracking Map", "InMaintenance": "In Maintenance", "ReceiveNotificationsOnTheWeb": "Receive notifications on the web", "ReceiveNotificationsInTheApp": "Receive notifications in the app", "MaintenanceAlerts": "Maintenance Alerts", "ImpactAlerts": "Impact Alerts", "OtherAlerts": "Other Alerts", "EquipmentDetails": "Equipment Details", "FaultInformation": "Fault Information", "FaultDateTime": "Fault Date & Time", "AlertType": "Alert <PERSON>", "SeverityLevel": "Severity Level", "FaultSymptoms": "Fault Symptoms", "FaultPhoto": "Fault Photo", "AlertSent": "<PERSON><PERSON>", "MaintenanceInformation": "Maintenance Information", "PartName": "Part Name", "ReplacementInterval": "Replacement Interval", "UsageTime": "Usage Time", "LastReplacementDate": "Last Replacement Date", "ReplacementDetail": "Replacement Detail", "DealershipInformation": "Dealership Information", "ServiceCenterInformation": "Service Center Information", "ServiceCenter": "Service Center", "Address": "Address", "SendAlert": "<PERSON> <PERSON><PERSON>", "DispatchInfortmation": "Dispatch Infortmation", "TotalTasks": "Total Tasks", "DeliveryAddress": "Delivery Address", "CompletionTime": "Completion Time", "VehicleInfo": "Vehicle Info", "Awaiting": "Awaiting", "Delievered": "Delievered", "Failed": "Failed", "InTransit": "In Transit", "TaskType": "Task Type", "BranchName": "Branch Name", "FullAddress": "Full Address", "DriverName": "Driver Name", "ArrivalTime": "Arrival Time", "Timeline": "Timeline", "VehicleInformation": "Vehicle Information", "DeliveryInformation": "Delivery Information", "MoterR": "<PERSON><PERSON>(R)", "MoterL": "<PERSON><PERSON>(L)", "TransmissionFluid": "Transmission Fluid", "NoFaultHistory": "고장 이력 없음", "NoConsumablesToInspectOrReplace.": "점검 또는 교체가 필요한 소모품 없음", "TodaysDistance": "Today's Distance", "TotalDistance": "Total Distance", "NewCases": "New Cases", "FaultAlert": "<PERSON><PERSON>", "ConsumableAlert": "Consumable Alert", "DrivingPattern": "Driving Pattern", "OverspeedCount": "Overspeed Count", "AverageSpeed": "Average Speed", "HarshBrakingCount": "Harsh Braking Count", "HarshAccelerationCount": "Harsh Acceleration Count", "DrivingTime": "Driving Time", "IdleTime": "Idle Time", "DrivingDistance": "Driving Distance", "30DayAverage": "30 Day \n Average", "VehicleLocation": "Vehicle Location", "ViewSpecifications": "View Specifications", "LocalDateTime": "Local Date & Time", "OperationStart": "Operation Start", "OperationEnded": "Operation Ended", "TotalOperationTime": "Total Operation Time", "Category": "Category", "OperationTime": "Operation Time", "OperationDate": "Operation Date", "AvgDailyTime": "Avg. Daily Time", "TotalFuelConsumption": "Total Fuel Consumption", "AverageDailyFuelConsumption": "Average Daily Fuel Consumption", "Lh": "L/h", "DrivingTimeN": "Driving\nTime", "IdleTimeN": "Idle\nTime", "OperationTimeRatio": "Operation Time Ratio", "Ratio": "<PERSON><PERSON>", "IdlingTime": "Idling Time", "CustomRange": "Custom Range", "FaultHistory": "Fault History", "AllFaultSeverity": "All Fault Severity", "AllFaultType": "All Fault Type", "AllStatus": "All Status", "High": "High", "Medium": "Medium", "Low": "Low", "FaultOccurenceTime": "<PERSON>ault Occurence Time", "FaultType": "Fault Type", "FaultImage": "Fault Image", "MaintenanceDetails": "Maintenance Details", "AlarmTypeN": "Alarm\nType", "FaultTypeN": "Fault\nType", "FaultSeverityN": "Fault\nSeverity", "FaultImageN": "Fault\nImage", "SendMessageNN": "Send\nMessage", "MaintenanceDetailsN": "Maintenance\nDetails", "Faults": "Faults", "Consumables": "Consumables", "MoreInfo": "More Info", "WorkHistory": "Work History", "Settings": "Settings", "CurrentFaultAlerts": "Current <PERSON><PERSON>s", "ConsumablesStatus": "Consumables Status", "ConsumablesHistory": "Consumables History", "CurrentFaultInformation": "Current Fault Information", "ConsumableItem": "Consumable Item", "ReplacementCycleDays": "Replacement Cycle(Days)", "LastMaintenanceMileageReplacementDate": "Last Maintenance\n(Mileage/Replacement Date)", "RemainingDays": "Remaining Days", "ConsumableReplacement": "Consumable Replacement", "IntervalUpdate": "Interval Update", "TaskDate": "Task Date", "ConfiguredValue": "Configured Value", "VehicleSpecifications": "Vehicle Specifications", "DrivingResistanceFactors": "Driving Resistance Factors", "AerodynamicDragCoefficient": "Aerodynamic Drag Coefficient", "TirePressure": "Tire Pressure", "TruckConfiguration": "Truck Configuration", "TireDiameter": "Tire Diameter", "TireWidth": "<PERSON><PERSON>", "HazardousMaterialsType": "Hazardous Materials Type", "NumberOfAxles": "Number of Axles", "NumberOfWheels": "Number of Wheels", "NumberOfTrailers": "Number of Trailers", "KingpinToRearAxleFtM": "Kingpin to Rear Axle(ft/m)", "LiftingEquipmentInstalled": "Lifting Equipment Installed", "RefrigerationUnitInstalled": "Refrigeration Unit Installed", "DeleteFleet": "Delete Fleet", "AreYouSureYouWantToDeleteThisFleetDeletedInformationCannotBeRecovered": "Are you sure you want to delete this fleet?\nDeleted information cannot be recovered.", "EquipmentList": "Equipment List", "EquipmentControl": "Equipment Control", "ControlHistory": "Control History", "ModelName": "Model Name ", "VehicleNumber ": "Vehicle Number ", "DealershipName": "Dealership Name", "EquipmentType": "Equipment Type", "AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheFleet": "Are you sure you want to remove the selected equipment\nfrom the fleet?", "RemoveEquipment": "Remove Equipment", "TheFleetHasBeenAdded": "The fleet has been added.", "EquipmentInformationN": "Equipment\nInformation", "AddSuccess": "Successfully added.", "AddFail": "Failed to add.", "DeleteSuccess": "Successfully deleted.", "DeleteFail": "Failed to delete.", "ModifySuccess": "Successfully modified.", "ModifyFail": "Failed to modify.", "AvailableEquipmentCount": "Available Equipment Count", "RegisterEquipment": "Register Equipment", "TheNewEquipmentHasBeenRegistered": "The new equipment has been registered.", "ImpactManagement": "Impact Management", "FleetImpactList": "List", "FleetImpactReport": "Report", "AllWorkStatus": "All Work Status", "NotStarted": "Not Started", "DeleteDriver": "Delete Driver", "AreYouSureYouWantToDeleteTheSelectedDriverDeletedInformationCannotBeRecovered": "Are you sure you want to delete the selected driver?\nDeleted information cannot be recovered.", "TheSelectedDriverHasBeenDeleted": "The selected driver has been deleted.", "Import": "Import", "DriverDetails": "Driver Details", "LicenseExpirationDate": "License Expiration Date", "AssignedEquipment": "Assigned Equipment", "WorkLog": "Work Log", "Trim": "<PERSON><PERSON>", "YearOfManufacture": "Year of Manufacture", "VINNumber": "VIN Number", "AssignableEquipment": "Assignable Equipment", "AreYouSureYouWantToDeleteTheSelectedEquipmentAssignedToThisDriver": "Are you sure you want to delete the selected equipment\nassigned to this driver?", "TheSelectedEquipmentHasBeenRemoved": "The selected equipment has been removed.", "DriverIDNumber": "Driver ID Number", "IssuingState": "Issuing State", "LicenseNumber": "License Number", "Class": "Class", "DriverInformationHasBeenUpdated": "Driver information has been updated.", "Drivers": "Drivers", "TotalWorkDuration": "Total Work Duration", "DistanceTraveled": "Distance Traveled", "ImpactInformationReport": "Impact Information Report", "OverspeedThreshold": "Overspeed Threshold", "Unsent": "Unsent", "ImpactThresholdG": "Impact Threshold (G)", "AccidentCriteriaG": "Accident Criteria (G)", "FrontRearSideVertical": "(Front/Rear, Side, Vertical)", "DeleteImpactInformation": "Delete Impact Information", "AreYouSureYouWantToDeleteTheSelectedImpactInformationDeletedDataCannotBeRecovered": "Are you sure you want to delete the selected impact information?Deleted data cannot be recovered.", "TheSelectedImpactInformationHasBeenDeleted": "The selected impact information has been deleted.", "UnitNo": "Unit No.", "ImpactThreshold": "Impact Threshold", "ValidRange01250": "Valid range: 0.1 ~ 25.0", "FontRear": "Font/Rear", "ValidRange01100": "Valid range: 0.1 ~10.0", "ValidRange130": "Valid range: 1 ~ 30", "ImpactTime": "Impact Time", "ImpactInformation": "Impact Information", "ThresholdSetting": "<PERSON><PERSON><PERSON><PERSON>", "EquipmentInformation": "Equipment Information", "AccidentCriteria": "Accident Criteria", "VehicleManagement": "Vehicle Management", "AllManufacture": "All Manufacture", "VehicleNo": "Vehicle No.", "NewestFirst": "Newest First", "LeaveThisPage": "Leave This Page?", "YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost": "You have unsaved changes.\nIf you leave this page now, all unsaved data will be lost.", "Leave": "Leave", "LoadDraft": "Load Draft", "SaveAsDraft": "Save as Draft", "BasicInformation": "Basic Information", "ConsumableItemsAndReplacementMaintenanceCriteria": "Consumable Items and Replacement/Maintenance Criteria", "Efficiency": "Efficiency", "FleetAssignment": "Fleet Assignment", "TrimName": "<PERSON>m Name", "ContactNumber": "Contact Number", "StreetAddress": "Street Address", "AptSuitUnit": "Apt/Suit/Unit", "ZIPCode": "ZIP Code", "TruckType": "Truck Type", "Length": "Length", "Height": "Height", "Width": "<PERSON><PERSON><PERSON>", "HazardousMaterials": "Hazardous Materials", "Explosives": "Explosives", "Gas": "Gas", "Flammable": "Flammable", "Organic": "Organic", "Poison": "Poison", "Radioactive": "Radioactive", "Corrosive": "Corrosive", "HarmfulForWater": "Harmful For Water", "PosionousInhaltionHazard": "Posionous Inhaltion Hazard", "Other": "Other", "mi": "mi", "km": "km", "EngineOil": "Engine Oil", "OilFilter": "Oil Filter", "FuelFilter": "Fuel Filter", "AirFilter": "Air Filter", "BrakePads": "<PERSON><PERSON><PERSON>", "BrakeLining": "<PERSON><PERSON><PERSON>", "Tires": "Tires", "Coolant": "Coolant", "TransmissionOil": "Transmission Oil", "FuelType": "Fuel Type", "SearchForAnAddress": "Search for an address", "FuelTankCapacity": "Fuel Tank Capacity", "FuelEfficiencyKmLi": "Fuel Efficiency(km/Li)", "FuelTankCapacityL": "Fuel Tank Capacity(L)", "SelectFleet": "Select Fleet", "Selected": "Selected", "BusinessName": "Business Name", "Inspect": "Inspect", "SafeDrivingScore": "Safe Driving Score", "ConsumablesManagement": "Consumables Management", "WorkLogSummary": "Work Log Summary", "TotalVehicles": "Total Vehicles", "Itinerary": "Itinerary", "OnDuty": "On Duty", "OffDuty": "Off Duty", "UserManagement": "User Management", "DealershipEquipmentStatus": "Dealership Equipment Status", "AllCountries": "All Countries", "AllDelarships": "All Delarships", "FaultCodeList": "Fault Code List", "ThereIsAVehicleInvolvedInAnAccident": "There is a vehicle involved in an accident.", "DeliveryStatus": "Delivery Status", "Incomplete": "Incomplete", "TotalEquipment": "Total Equipment", "Waypoint": "Waypoint", "AddWaypoint": "Add Waypoint", "TitleOrContent": "Title or Content", "AllNotice": "All Notice", "SystemMaintenance": "System Maintenance", "TermsUpdate": "Terms Update", "GeneralUpdate": "General Update", "NoticeType": "Notice Type", "Author": "Author", "Views": "Views", "PostedDate": "Posted Date", "AreYouSureYouWantToDeleteThisNotice": "Are you sure you want to delete this notice?", "TheNoticeHasBeenDeleted": "The notice has been deleted.", "DisplayPeriod": "Display Period", "AlwaysVisible": "Always Visible", "ScheduledPost": "Scheduled Post", "ViewPermission": "View Permission", "PinnedToTop": "Pinned to Top", "AreYouSureYouWantToDeleteThisYemporarySaveThisActionCannotBeUndone": "Are you sure you want to delete this temporary save?\nThis action cannot be undone.", "TheTemporarySaveHasBeenDeleted": "The temporary save has been deleted.", "AllInquiryType": "All Inquiry Type", "InquiryType": "Inquiry Type", "Pinned": "Pinned", "DeleteNotice": "Delete Notice", "DeleteFAQ": "Delete FAQ", "AreYouSureYouWantToDeleteThisFAQ": "Are you sure you want to delete this FAQ?", "TheFAQHasBeenDeleted": "The FAQ has been deleted.", "401error": "401error", "YouAreNotAuthorizedToAccessThisPagePleaseLogInAndTryAgain": "You are not authorized to access this page.\nPlease log in and try again.", "IdleVehicles": "Idle Vehicles", "UnderMaintenance": "Under Maintenance", "Descending": "Descending", "Ascending": "Ascending", "NoReportedIssuesForThisVehicle": "No reported issues for this vehicle.", "NoConsumablesRequireInspectionOrReplacement": "No consumables require inspection or replacement.", "NoDealerInformationAvailable": "No dealer information available.", "NoServiceCenterInformationAvailable": "No service center information available.", "DriveType": "Drive Type", "SetInterval": "Set Interval", "ManufacturerName": "Manufacturer Name", "DispatchHistory": "Dispatch History", "ItineraryTitle": "Itinerary Title", "ItineraryDate": "Itinerary Date", "TotalStops": "Total Stops", "CompletedDeliveries": "Completed Deliveries", "SearchResultsNotFound": "Search results not found.", "NoAddressesFound": "No addresses found.", "Searching": "Searching", "VehicleAssignedToDriver": "Vehicle Assigned to Driver", "SelectOtherVehicle": "Select Other Vehicle", "DispatchDetails": "Dispatch Details", "ItienearyDate": "Itieneary Date", "ItineraryInformation": "Itinerary Information", "VehicleList": "Vehicle List", "VehicleControl": "Vehicle Control", "AddVehicle": "Add Vehicle", "VINNo": "VIN No.", "ManufactureYear": "Manufacture Year", "VehicleType": "Vehicle Type", "DriversLicenseNumber": "Driver's License Number", "ManufactureName": "Manufacture Name", "BodyClass": "Body Class", "TireRotation": "Tire Rotation", "TireWidthmm": "Tire Width(mm)", "TireDiameterinch": "Tire Diameter(inch)", "SelectDealer": "Select Dealer", "SelectServiceCenter": "Select Service Center", "HighTemperatureThreshold": "High Temperature Threshold", "LowTemperatureThreshold": "Low Temperature Threshold", "DeliveryOrder": "Delivery Order", "RemoveVehicle": "Remove Vehicle", "TheSelectedVehicleHasBeenUnassignedFromDriver": "The selected vehicle has been unassigned from driver.", "PleaseChooseHowYoudLikeToRegisterDrivers": "Please choose how you'd like to register drivers.", "UploadViaExcel": "Upload via Excel", "RegisterManually": "Register Manually", "DragAndDropAfileOrClick": "Drag and Drop a file or click.", "DontHaveTheDriverRegistrationFormClickTheButtonBelowToDownloadIt": "Don't have the driver registration form?\nClick the button below to download it.", "CancelUpload": "Cancel Upload", "UploadingDriverListPleaseWaitAMoment": "Uploading driver list...\nPlease wait a moment.", "TheDriverListUploadHasNotBeenCompletedDoYouWantToCancelTheUpload": "The driver list upload has not been completed.\nDo you want to cancel the upload?", "UploadCanceled": "Upload Canceled", "IncidentManagement": "Incident Management", "DealerServiceCenterManagement": "Dealer & Service Center Management", "DriverListHasBeenSuccessfullyUploaded": "Driver list has been successfully uploaded.", "PhoneNumberNoDashes": "Phone number (no dashes)", "812Characters": "8 - 12 characters.", "MustIncludeAtLeast3OfTheFollowing": "Must include at least 3 of the following:", "UppercaseLettersAZ": "- Uppercase letters (A-Z)", "LowercaseLettersaz": "- Lowercase letters (a-z)", "Numbers09": "- Numbers (0-9)", "SpecialCharacters!@#$%^*()-_=+~": "- Special characters ( !@#$%^*()-_=+~ )", "CannotContain3IdenticalOrSequentialCharacters": "Cannot contain 3 identical or sequential characters (e.g. aaa, 123)", "LicenseInformation": "License Information", "ExpirationDate": "Expiration Date", "BasicInformationOptional": "Basic Information(Optional)", "Gender": "Gender", "VehicleRegistration": "Vehicle Registration", "IfYouHaveACompanyAssignedIDEGEmployeeNumberPleaseEnterIt": "If you have a company-assigned ID (e.g., employee number), please enter it.", "DriverHasBeenRegistered": "Driver has been registered.", "AccidentManagement": "Accident Management", "Sedan": "Sedan", "Hatchback": "Hatchback", "Coupe": "Coupe", "Converible": "Converible", "SUV": "SUV", "VanMinivan": "Van / Minivan", "Wagon": "Wagon", "PickupTruck": "Pickup Truck", "BusMinibus": "Bus / Minibus", "HeavyDutyTruck": "Heavy-Duty Truck", "OffRoadVehicle": "Off-Road Vehicle", "Trailer": "Trailer", "ThreeWheelMotocycle": "Three-Wheel Motocycle", "IncompleteVehicle": "Incomplete Vehicle", "Diesel": "Diesel", "Gasoline": "Gasoline", "Hybrid": "Hybrid", "Electric": "Electric", "Ft": "ft", "In": "in", "MKm": "m/Km", "FtMi": "ft/mi", "NotSubmitted": "Not Submitted", "Submitted": "Submitted", "ChangingTheStatusToMaintenanceCompletedWillLeadToTheReportSubmissionStep": "* Changing the status to 'Maintenance Completed' will lead to the report submission step.", "ChangeStatus": "Change Status", "VehicleSelect": "Vehicle Select", "NumberNoDashes": "Number (no dashes)", "IfYouHaveAnEmployeeIDOrInternalManagementIDPleaseEnterIt": "If you have an employee ID or internal management ID, please enter it.", "Stay": "Stay", "YouHaveUnsavedChangesIfYouLeaveNowAllEnteredInformationWillBeLostTheMaintenanceStatusWillNotBeUpdatedToCompletedUnlessTheReportIsSubmitted": "You have unsaved changes. If you leave now, all entered information will be lost. The maintenance status will not be updated to 'Completed' unless the report is submitted.", "Validate": "Validate", "Ph": "Ph.", "SetReplacementInterval": "Set Replacement Interval", "TheReplacementIntervalHasBeenUpdated": "The replacement interval has been updated.", "AreYouSureYouWantToResetTheReplacementDaysForThisConsumableItem": "Are you sure you want to reset the replacement days for this consumable item?", "ResetReplacementDays": "Reset Replacement Days", "NoHistory": "No History.", "EngineConsumption": "Engine Consumption", "DealerServiceCenter": "Dealer/Service Center", "AccidentHistory": "Accident History", "LithiumBatteryInfo": "Lithium Battery Info", "CautionTheMessageBelowWillBeSentTheTheDriverViaAppPushNotification": "* Caution : The message below will be sent the the driver via app push nofitication.", "DailyAverage": "Daily Average", "WorkTime": "Work Time", "EngineOperationTime": "Engine Operation Time", "FuelConsumptionECM": "Fuel Consumption(ECM)", "FuelConsumptionTank": "Fuel Consumption(Tank)", "PleaseFillInTheRequiredFields": "Please fill in the required fields.", "AResetLinkWillBeSentToTheDriversEmailEmailRequestsAreLimitedToOncePerMinute": "A reset link will be sent to the driver's email.\nEmail requests are limited to once per minute.", "DriverNameIsRequired": "Driver name is required.", "DriverNameCannotExceed20Characters": "Driver name cannot exceed 20 characters.", "OnlyKoreanAndEnglishLettersAreAllowed": "Only Korean and English letters are allowed.", "CountryIsRequired": "Country is required.", "PhoneNumberIsRequired": "Phone number is required.", "PhoneNumberMustBeNumericAndUpTo15Digits": "Phone number must be numeric and up to 15 digits.", "EmailIsRequired": "Email is required.", "EmailCannotExceed64Characters": "Email cannot exceed 64 characters.", "InvalidEmailAddress": "Invalid email address.", "PasswordIsRequired": "Password is required.", "PasswordMustBeAtLeast8Characters": "Password must be at least 8 characters.", "PasswordMustBeAtMost12Characters": "Password must be at most 12 characters.", "PasswordMustIncludeLettersNumbersAndSpecialCharacters": "Password must include letters, numbers, and special characters.", "PasswordCannotContainSameCharacterRepeated3Times": "Password cannot contain same character repeated 3 times.", "IssuingStateIsRequired": "Issuing state is required.", "LicenseNumberIsRequired": "License number is required.", "ClassIsRequired": "Class is required.", "ExpirationDateIsRequired": "Expiration date is required.", "IDNumberCannotExceed20Characters": "ID number cannot exceed 20 characters.", "FAQRegistration": "FAQ Registration", "FAQModify": "FAQ Modify", "NoticeRegistration": "Notice Registration", "NoticeModify": "Notice Modify", "TheFAQHasBeenPosted": "The FAQ has been posted.", "YourDraftHasBeenSaved": "Your draft has been saved.", "DealerServiceCenterInformation": "Dealer/Service Center Information", "DealerCompanyName": "Dealer Company Name", "AccidentTime": "Accident Time", "ResponseStatus": "Response Status", "TotalChargeCycles": "Total Charge Cycles", "BTMSPowerConsumption": "BTMS Power Consumption", "MaximumBatteryTemperature": "Maximum Battery Temperature", "CoolingFanUsage": "Cooling Fan Usage", "BatteryPackCycleCount": "Battery Pack Cycle Count", "MaximumCellVoltage": "Maximum Cell Voltage", "MinimumCellVoltage": "Minimum Cell Voltage", "MaximumModuleTemperature": "Maximum Module Temperature", "MinimumModuleTemperature": "Minimum Module Temperature", "CoolentTemperature": "Coolent Temperature", "BatteryToBTMS": "Battery to BTMS", "BTMSToVehicle": "BTMS to Vehicle", "VehicleToBTMS": "Vehicle to BTMS", "AssignedVehicle": "Assigned Vehicle", "aaa": "aaa"}}