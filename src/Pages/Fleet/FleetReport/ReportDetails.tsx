import SummaryData from '@/Common/Components/etc/SummaryData';
import useDimension from '@/hooks/useDimension.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Button } from '@/Common/Components/common/Button';
import AverageWorkTime from '@/Pages/Statistics/components/ProductivityEfficiency/AverageWorkTime.tsx';
import EqOperationRate from '@/Pages/Statistics/components/ProductivityEfficiency/EQOperationRate.tsx';
import EqWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/EqWorkTimeRanking.tsx';
import JobRestTime from '@/Pages/Statistics/components/ProductivityEfficiency/JobRestTime.tsx';
import StatModelWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/StatModelWorkTimeRanking.tsx';
import WorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkTimeRanking.tsx';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { useRef } from 'react';
import { handleCapture } from '@/Common/function/pdf.ts';

const ReportDetails = () => {
  const { t } = useTranslation();

  const captureRef = useRef(null);
  const pdfFileName: string = 'Report.pdf';

  const summaryData = [
    { label: t('Model'), value: 'R220LC-98' },
    { label: t('FleetReport'), value: '0014' },
    { label: t('SerialNo'), value: 'US0014' },
  ];

  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(4열), md: 태블릿(2열), sm: 작은 태블릿(2열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 1, h: 50, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 50, static: false },
      { i: 'c', x: 2, y: 0, w: 2, h: 30, static: false },
      { i: 'd', x: 2, y: 1, w: 2, h: 20, static: false },
      { i: 'e', x: 0, y: 2, w: 2, h: 39, static: false },
      { i: 'f', x: 2, y: 2, w: 2, h: 39, static: false },
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 1, h: 50, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 50, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 30, static: false },
      { i: 'd', x: 0, y: 2, w: 2, h: 20, static: false },
      { i: 'e', x: 0, y: 3, w: 2, h: 39, static: false },
      { i: 'f', x: 0, y: 4, w: 2, h: 39, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 1, h: 50, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 50, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 30, static: false },
      { i: 'd', x: 0, y: 2, w: 2, h: 20, static: false },
      { i: 'e', x: 0, y: 3, w: 2, h: 39, static: false },
      { i: 'f', x: 0, y: 4, w: 2, h: 39, static: false },
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 1, h: 50, static: false },
      { i: 'b', x: 0, y: 1, w: 1, h: 50, static: false },
      { i: 'c', x: 0, y: 2, w: 1, h: 30, static: false },
      { i: 'd', x: 0, y: 3, w: 1, h: 20, static: false },
      { i: 'e', x: 0, y: 4, w: 1, h: 39, static: false },
      { i: 'f', x: 0, y: 5, w: 1, h: 39, static: false },
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 4, md: 2, sm: 2, xs: 1 };

  const { width } = useDimension();
  const gridWidth = width > 1024 ? width - 330 : width - 72;

  const ecoIndexData = [
    { name: t('WorkingO'), value: 50 },
    { name: t('NonOperation'), value: 30 },
    { name: t('Repair'), value: 20 },
  ];

  const rankingRows = [
    {
      name: 'HXDEMO',
      number: '0819',
      where: 'US-01',
      hitNumber: 219,
      time: '219 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-01',
      number: '0820',
      hitNumber: 211,
      time: '211 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-02',
      number: '0828',
      hitNumber: 208,
      time: '208 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-03',
      number: '0830',
      hitNumber: 105,
      time: '105 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-02',
      number: '0862',
      hitNumber: 98,
      time: '98 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-03',
      number: '0882',
      hitNumber: 96,
      time: '96 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-01',
      number: '0900',
      hitNumber: 96,
      time: '96 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-02',
      number: '0990',
      hitNumber: 95,
      time: '95 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-03',
      number: '0992',
      hitNumber: 91,
      time: '91 Hours',
      upDown: false,
    },
  ];

  const workData = [
    { issueDate: '2024-06-01', workingHour: 10, travelHour: 5, idleHour: 2 },
    { issueDate: '2024-06-02', workingHour: 12, travelHour: 4, idleHour: 3 },
  ];

  return (
    <CustomFrame name={t('Report')} back={true}>
      <section ref={captureRef}>
        <div className="divider mt-0"></div>

        {/* 요약 */}
        <article className="flex items-center justify-between">
          <SummaryData details={summaryData} fs="lg" />
          <Button
            variant={'bt_primary'}
            label={'PDFDownload'}
            onClick={() => handleCapture(captureRef, pdfFileName)}
          />
        </article>

        {/* 대시보드 */}
        <article>
          <div className="heading2 my-10 pb-3 border-b border-gray-2">
            {t('ProductivityEfficiencyAnalysis')}
          </div>
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            breakpoints={breakpoints}
            cols={cols}
            isDroppable={false}
            isDraggable={false}
            rowHeight={1}
            width={gridWidth}
            margin={[16, 16]}
            containerPadding={[16, 16]}
          >
            <div className="p-4" key="a">
              <EqOperationRate
                title={t('MachineUtilizationRate')}
                data={ecoIndexData}
              />
            </div>
            <div className="p-4" key="b">
              <AverageWorkTime
                title={t('AverageWorkingHoursE')}
                data={ecoIndexData}
              />
            </div>
            <div className="p-4" key="c">
              <WorkTimeRanking workData={workData} />
            </div>
            <div className="p-4" key="d">
              <JobRestTime workData={workData} />
            </div>
            <div className="p-4" key="e">
              <StatModelWorkTimeRanking eq={[]} />
            </div>
            <div className="p-4" key="f">
              <EqWorkTimeRanking eq={rankingRows} />
            </div>
          </ResponsiveGridLayout>
        </article>
      </section>
    </CustomFrame>
  );
};

export default ReportDetails;
