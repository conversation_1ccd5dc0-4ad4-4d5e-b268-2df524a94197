import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Cross1Icon } from '@radix-ui/react-icons';

const ShockInfoPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData1 = [
    { label: t('Fleet'), value: 'Fleet 1' },
    { label: t('ModelName'), value: '100B-9V' },
    { label: t('UnitNo'), value: '100B9V-12345' },
    { label: t('AssetID'), value: 'CA-3241' },
  ];

  const summaryData2 = [
    {
      label: t('FrontRearCollision'),
      value: <span className="text-semantic-4">2.3G</span>,
    },
    { label: t('SideCollision'), value: '1.9G' },
    { label: t('VerticalCollision'), value: '1.7G' },
  ];

  const summaryData3 = [
    { label: t('FrontRearAccident'), value: '3.2G' },
    {
      label: t('SideAccident'),
      value: <span className="text-semantic-4">3.6G</span>,
    },
    { label: t('VerticalAccident'), value: '1.7G' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[780px] popup-wrap">
        {/*  */}
        <article>
          <h2> {t('ThresholdSetting')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 장비 정보 */}
        <article className="space-y-[30px] [&_h3]:w-[170px] [&_h3]:subtitle4">
          {/*  */}
          <div className="f-s gap-5">
            <h3>{t('EquipmentInformation')}</h3>
            <SummaryData details={summaryData1} fs="lg" />
          </div>

          <div className="divider" />

          {/*  */}
          <div className="f-c gap-5">
            <h3>{t('ImpactThreshold')}</h3>
            <SummaryData details={summaryData2} fs="lg" />
          </div>

          {/*  */}
          <div className="f-c gap-5">
            <h3>{t('AccidentCriteria')}</h3>
            <SummaryData details={summaryData3} fs="lg" />
          </div>

          <div className="pt-[10px] f-je">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default ShockInfoPopup;
