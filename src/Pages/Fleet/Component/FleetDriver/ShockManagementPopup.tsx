import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import Input from '@/Common/Components/common/Input';
import { Cross1Icon } from '@radix-ui/react-icons';

const ShockManagementPopup = ({
  isOpen,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData = [
    { label: t('Fleet'), value: 'Fleet 1' },
    { label: t('ModelName'), value: '100B-9V' },
    { label: t('UnitNo'), value: '100B9V-12345' },
    { label: t('AssetID'), value: 'CA-3241' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[780px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('ManageCollision2')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 요약 */}
        <article>
          {/*  */}
          <div className="mb-[30px] pb-[30px] f-s gap-5 border-b border-gray-6">
            <div className="flex-shrink-0 subtitle4">
              {t('MachineInformation')}
            </div>
            <SummaryData details={summaryData} fs="lg" />
          </div>

          {/* 정보 입력 */}
          <div
            className="
              mb-10 grid grid-cols-2 gap-[60px]
              [&>div]:col-span-1
              [&>div:last-child]:col-span-2
              [&>div]:space-y-3
              [&>div]:[&>div]:f-c
              [&_h2]:mr-3
              [&_h2]:flex-shrink-0
              [&_h2]:subtitle4
              [&_h3]:w-[85px]
              [&_h3]:flex-shrink-0
              [&_h3]:body1
              [&_p]:ml-[6px]
              [&_p]:captipn1
              [&_span]:caption3
              [&_em]:text-semantic-4
            "
          >
            {/* 충격 기준 */}
            <div>
              <div>
                <h2>{t('ImpactThreshold')}</h2>
                <span>
                  <em>*</em> {t('ValidRange01250')}
                </span>
              </div>
              <div>
                <h3>{t('FontRear')}</h3>
                <Input placeholder="FontRear" />
                <p>G</p>
              </div>
              <div>
                <h3>{t('Side')}</h3>
                <Input placeholder="Side" />
                <p>G</p>{' '}
              </div>
              <div>
                <h3>{t('Vertical')}</h3>
                <Input placeholder="Vertical" />
                <p>G</p>
              </div>
            </div>
            {/* 사고 기준 */}
            <div>
              <div>
                <h2>{t('RefAccident')}</h2>
                <span>
                  <em>*</em> {t('ValidRange01100')}
                </span>
              </div>
              <div>
                <h3>{t('FontRear')}</h3>
                <Input placeholder="FontRear" />
                <p>G</p>
              </div>
              <div>
                <h3>{t('Side')}</h3>
                <Input placeholder="Side" />
                <p>G</p>
              </div>
              <div>
                <h3>{t('Vertical')}</h3>
                <Input placeholder="Vertical" />
                <p>G</p>
              </div>
            </div>
            <div>
              <div>
                <h2>{t('OverspeedThreshold')}</h2>
                <span>
                  <em>*</em> {t('ValidRange130')}
                </span>
              </div>
              <div>
                <h3 style={{ width: '180px' }}>{t('Overspeed Threshold')}</h3>
                <div className="w-[200px]">
                  <Input placeholder="FontRear" />
                </div>
                <p>{t('Kmh')}</p>
              </div>
            </div>
          </div>

          {/* 버튼 */}
          <div className="f-c-e gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
            <Button
              variant={'bt_primary'}
              label={'Confirm'}
              onClick={onConfirm}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default ShockManagementPopup;
