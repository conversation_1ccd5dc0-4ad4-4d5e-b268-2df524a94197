import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Cross1Icon } from '@radix-ui/react-icons';

const SendItemPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData = [
    { label: t('Model'), value: '25B-X' },
    { label: t('MachineID'), value: '00010' },
    { label: t('SerialNo'), value: '삼호 10-0001' },
  ];

  const columns = [
    {
      header: t('No'),
      accessorKey: 'no',
    },
    {
      header: t('ControlName'),
      accessorKey: 'control',
    },
    {
      header: t('TransmissionStatus'),
      accessorKey: 'state',
    },
    {
      header: t('DateTime'),
      accessorKey: 'date',
    },
  ];
  const data = [
    {
      no: '1',
      control: '클러스터 비밀번호',
      state: 'Success',
      date: '2025-01-02 12:11',
    },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1236px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 mb-[34px] flex items-center justify-between">
          {t('ViewHistoryE')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 요약 */}
        <article className="mb-8 flex justify-start">
          <div className="body1-b mr-[26px]">{t('MachineInformation')}</div>
          <SummaryData details={summaryData} fs="lg" />
        </article>

        {/* 테이블 */}
        <article className="border-t border-x border-gray-1">
          <CommonTable columns={columns} data={data} isPagination={false} />
        </article>

        {/* 버튼 */}
        <article className="mt-10 flex justify-end">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
        </article>
      </section>
    </Layout>
  );
};

export default SendItemPopup;
