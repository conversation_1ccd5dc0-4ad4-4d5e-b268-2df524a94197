import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

// 추가: 팝업에서 사용할 데이터 타입 정의 (props로 받음)
interface FleetEquipmentData {
  regDt?: string;
  responseDt?: string;
  mileage?: number;
  claimId?: string;
  claimType?: string;
  inspection?: string;
}

interface ServiceHistoryPopupProps extends AlertPopupProps {
  fleetEquipmentData?: FleetEquipmentData; // 외부에서 주입
}

const ServiceHistoryPopup = ({
  onClose,
  isOpen,
  fleetEquipmentData,
}: ServiceHistoryPopupProps) => {
  const { t } = useTranslation();

  // 더미 데이터(테스트/예시용)
  const data = fleetEquipmentData || {
    regDt: '2025-01-01',
    responseDt: '2025-01-02',
    mileage: 1250,
    claimId: 'CLM-2025-01',
    claimType: 'A',
    inspection: '정기점검 및 부품 교체<br>정상 작동 확인됨.',
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[800px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 flex items-center justify-between">
          {t('SettingHistory')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article className="mt-[34px] space-y-6 flex items-start flex-col [&_span]:w-[110px] [&_span]:mr-[26px] ">
          {/* 고장 발생일 */}
          <SearchItemContainer className="gap-0">
            <SearchLabel className="w-[110px]">
              {t('FaultOccurrenceDate')}
            </SearchLabel>
            <div className="body1-s">{data.regDt}</div>
          </SearchItemContainer>
          {/* 수리 완료일 */}
          <SearchItemContainer className="gap-0">
            <SearchLabel className="w-[110px]">
              {t('FaultResolutionDate2')}
            </SearchLabel>
            <div className="body1-s">{data.responseDt}</div>
          </SearchItemContainer>
          {/* 가동 시간 */}
          <div className="flex items-center">
            <span className="body1-b">
              {t('OperatingTime')} <em className="text-error">*</em>
            </span>
            <div className="body1-s">{data.mileage?.toLocaleString()}</div>
          </div>
          {/* 조치자 */}
          {/* 클레임 정보 */}
          <div className="flex items-center">
            <span className="body1-b">{t('ClaimInformation')}</span>
            <div className="space-x-[30px] body1-s [&_em:nth-child(odd)]:text-gray-9">
              <em>{t('ClaimNumber')}</em>
              <em className="pr-3">{data.claimId}</em>
              <em>{t('ClaimType')}</em>
              <em>
                {data.claimType === 'A' ? t('AlarmF') : t('MaintenanceAlert')}
              </em>
            </div>
          </div>
          {/* 점검 내용 */}
          <div className="flex items-start justify-between">
            <span className="body1-b">
              {t('InspectionDetails')} <em className="text-error">*</em>
            </span>
            <div
              className="body3-m w-[525px] min-h-[200px] pr-10"
              dangerouslySetInnerHTML={{
                __html: (data.inspection || '').replace(/\n/g, '<br>'),
              }}
            />
          </div>
        </article>

        {/* 버튼 */}
        <div className="mt-10 flex justify-end space-x-3">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
        </div>
      </section>
    </Layout>
  );
};

export default ServiceHistoryPopup;
