import { useTranslation } from 'react-i18next';
import { useOverlay } from '@toss/use-overlay';
import { useToast } from '@/Common/useToast.tsx';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import FleetAddPopup from '@/Pages/Fleet/Component/FleetManagement/FMFleetAddPopup';
import MAddDriverPopup from '@/Pages/Fleet/Component/FleetManagement/AddDriverPopup.tsx';
import FMAddVehiclePopup from '@/Pages/Fleet/Component/FleetManagement/FMAddVehiclePopup';
import MEqSettingPopup from '@/Pages/Fleet/Component/FleetManagement/EqSettingPopup.tsx';
import MSendItemPopup from '@/Pages/Fleet/Component/FleetManagement/SendItemPopup.tsx';
import MSpecifyingFleetPopup from '@/Pages/Fleet/Component/FleetManagement/SpecifyingFleetPopup.tsx';
import FDDriverRegistrationPopup from '@/Pages/Fleet/Component/FleetDriver/FDDriverRegistrationPopup';
import FDDriverEqAddPopup from '@/Pages/Fleet/Component/FleetDriver/FDDriverEqAddPopup';
import DModificationDriverPopup from '@/Pages/Fleet/Component/FleetDriver/FDDriverModPopup';
import DShockInfoPopup from '@/Pages/Fleet/Component/FleetDriver/ShockInfoPopup.tsx';
import DShockManagementPopup from '@/Pages/Fleet/Component/FleetDriver/ShockManagementPopup.tsx';
import FleetDelPopup from './FleetManagement/FMFleetDelPopup';
import FMEquipmentDelPopup from './FleetManagement/FMEquipmentDelPopup';
import FDDriverDelPopup from './FleetDriver/FDDriverDelPopup';
import FDDriverEqDelPopup from './FleetDriver/FDDriverEqDelPopup';

const UseFleetPopup = () => {
  const { t } = useTranslation();

  const overlay = useOverlay();

  const { toast } = useToast();

  // 모든 API/mutation 삭제, 단순 팝업 트리거 및 toast만 남김

  const openFmDeletePopup = (fleets?: number[], refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <FleetDelPopup
          fleets={fleets}
          onClose={close}
          onConfirm={() => {
            if (refreshList) {
              refreshList();
            }
            close();
          }}
          isOpen={isOpen}
          title={t('DeleteFleet')}
          secondButtonText={t('Cancel')}
          buttonText={t('Delete')}
          text={t(
            'AreYouSureYouWantToDeleteThisFleetDeletedInformationCannotBeRecovered',
          )}
        />
      );
    });
  };

  const openFmFleetAddPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <FleetAddPopup
          onClose={close}
          onConfirm={() => {
            close();
            if (refreshList) {
              refreshList();
            }
          }}
          isOpen={isOpen}
        />
      );
    });
  };
  const openFmDriverDeletePopup = () => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'warning',
            description: t('TheDriverHasBeenRemoved'),
          });
          close();
        }}
        isOpen={isOpen}
        text={t('AreYouSureYouWantToRemoveTheSelectedDriverFromTheEquipment')}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };
  const openFDDriverDelPopup = (
    driverIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FDDriverDelPopup
        driverIds={driverIds}
        onClose={close}
        onConfirm={() => {
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('DeleteDriver')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedDriverDeletedInformationCannotBeRecovered',
        )}
      />
    ));
  };
  const openFDDriverEqDelPopup = (
    driverId: number,
    equipmentIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FDDriverEqDelPopup
        driverId={driverId}
        equipmentIds={equipmentIds}
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheSelectedVehicleHasBeenUnassignedFromDriver'),
          });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('RemoveVehicle')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedEquipmentAssignedToThisDriver',
        )}
      />
    ));
  };
  const openFvBackPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('LeaveThisPage')}
        text={t(
          'YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('Leave')}
      />
    ));
  };
  // 이하 팝업은 모두 UI만 표시, 내부 함수/토스트 호출 동일하게 유지
  const openMAddDriverPopup = () => {
    overlay.open((p) => (
      <MAddDriverPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('ANewDriverHasBeenRegistered'),
          });
          p.close();
        }}
      />
    ));
  };
  const openFMAddVehiclePopup = (fleetId: number, refreshList?: () => void) => {
    overlay.open((p) => (
      <FMAddVehiclePopup
        fleetId={fleetId}
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          refreshList?.();
          p.close();
        }}
      />
    ));
  };
  const openFMEquipmentDelPopup = (
    fleetId: number,
    equipmentIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FMEquipmentDelPopup
        fleetId={fleetId}
        equipmentIds={equipmentIds}
        onClose={close}
        onConfirm={() => {
          if (refreshList) refreshList();
          close();
        }}
        isOpen={isOpen}
        title={t('RemoveVehicle')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t('AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheFleet')}
      />
    ));
  };
  const openMEqSettingPopup = () => {
    overlay.open((p) => (
      <MEqSettingPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openMSendItemPopup = () => {
    overlay.open((p) => (
      <MSendItemPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openMSpecifyingFleetPopup = () => {
    overlay.open((p) => (
      <MSpecifyingFleetPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheFleetHasBeenAssigned'),
          });
          p.close();
        }}
      />
    ));
  };
  const openFDDriverRegistrationPopup = (options?: {
    onManualRegister?: () => void;
  }) => {
    overlay.open((p) => (
      <FDDriverRegistrationPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('DriverListHasBeenSuccessfullyUploaded'),
          });
          p.close();
        }}
        onManualRegister={options?.onManualRegister}
      />
    ));
  };
  const openFDDriverEqAddPopup = (
    driverId: number,
    refreshList?: () => void,
  ) => {
    overlay.open((p) => (
      <FDDriverEqAddPopup
        driverId={driverId}
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          refreshList?.();
          p.close();
        }}
      />
    ));
  };
  const openFDPageOutPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          // toast({
          //   types: 'success',
          //   description: t('DriverHasBeenRegistered'),
          // });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('LeaveThisPage')}
        text={t(
          'YouHaveUnsavedChangesIfYouLeaveNowAllEnteredInformationWillBeLostTheMaintenanceStatusWillNotBeUpdatedToCompletedUnlessTheReportIsSubmitted',
        )}
        buttonText={t('Stay')}
        secondButtonText={t('Leave')}
      />
    ));
  };
  const openFDDriverModPopup = () => {
    overlay.open((p) => (
      <DModificationDriverPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('DriverInformationHasBeenUpdated'),
          });
          p.close();
        }}
      />
    ));
  };
  const openDImpactDeletePopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheSelectedImpactInformationHasBeenDeleted'),
          });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('DeleteImpactInformation')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedImpactInformationDeletedDataCannotBeRecovered',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };
  const openDShockInfoPopup = () => {
    overlay.open((p) => (
      <DShockInfoPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openDShockManagementPopup = () => {
    overlay.open((p) => (
      <DShockManagementPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheUpdatedInformationHasBeenSaved'),
          });
          p.close();
        }}
      />
    ));
  };

  return {
    openFmDeletePopup,
    openFmFleetAddPopup,
    openFmDriverDeletePopup,
    openFDDriverDelPopup,
    openFDDriverEqDelPopup,
    openFvBackPopup,
    openMAddDriverPopup,
    openFMAddVehiclePopup,
    openFMEquipmentDelPopup,
    openMEqSettingPopup,
    openMSendItemPopup,
    openMSpecifyingFleetPopup,
    openFDDriverRegistrationPopup,
    openFDDriverEqAddPopup,
    openFDPageOutPopup,
    openFDDriverModPopup,
    openDImpactDeletePopup,
    openDShockInfoPopup,
    openDShockManagementPopup,
  };
};

export default UseFleetPopup;
