import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Dropdown from '@/Common/Components/common/DropDown';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';

const FleetEquipmentInfo = () => {
  const { t } = useTranslation();
  const { openMSpecifyingFleetPopup } = UseFleetPopup();
  const navigate = useNavigate();

  const routeDetails = () => {
    navigate('/fleet_management/eqDetails');
  };

  const routeCell = ({ cell }: { cell: { getValue: () => unknown } }) => (
    <span onClick={routeDetails} className="cursor-pointer">
      {cell.getValue() as string}
    </span>
  );

  const fleetOptions = [
    { key: 'Fleet1', value: 'fleet1' },
    { key: 'Fleet2', value: 'fleet2' },
    { key: 'Fleet3', value: 'fleet3' },
  ];

  const columns = [
    {
      header: t('Fleet'),
      accessorKey: 'fleet',
      cell: () => (
        <Dropdown
          size={'no'}
          className={'bg-transparent border-0'}
          onChange={() => undefined}
          options={fleetOptions}
          placeholder={'Fleet1 +2'}
        />
      ),
    },
    {
      header: t('Model'),
      accessorKey: 'model',
      cell: routeCell,
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
      cell: routeCell,
    },
    {
      header: t('SerialNo'),
      accessorKey: 'num',
      cell: routeCell,
    },
    {
      header: t('RMCU'),
      accessorKey: 'rmcu',
      cell: routeCell,
    },
    {
      header: t('Country'),
      accessorKey: 'nation',
      cell: routeCell,
    },
    {
      header: t('Customer'),
      accessorKey: 'customer',
      cell: routeCell,
    },
    {
      header: t('Driver'),
      accessorKey: 'driver',
      cell: () => <span className="blue-underline">{t('View')}</span>,
    },
  ];
  const data = [
    {
      fleet: 'Fleet1',
      model: '25B-X',
      unit: '00010',
      num: 'HHKHFT23JF0000919',
      rmcu: 'TRW1D0220616',
      nation: '대한민국',
      customer: '',
      driver: 'driver',
    },
  ];

  return null;

  return (
    <Tabs.Content value={'EquipmentInfo'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="mb-10 flex items-center justify-between gap-3">
          <div className="flex items-center gap-6">
            <SearchItemContainer>
              <SearchLabel>{t('Country')}</SearchLabel>
              <DropDown
                onChange={() => {}}
                options={[]}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Model')}</span>
              <Input placeholder={t('Model')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('MachineID')}</span>
              <Input placeholder={t('MachineID')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('SerialNo')}</span>
              <Input placeholder={t('SerialNo')} />
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant={'bt_primary'} label={'Search'} />
            <Button variant={'bt_primary'} label={'Print'} />
          </div>
        </div>
      </article>

      {/* 테이블 */}
      <article>
        <Button
          variant={'bt_primary'}
          label={'SpecifyFleet'}
          onClick={openMSpecifyingFleetPopup}
          className="mb-3"
        />
        <CommonTable
          columns={columns}
          data={data}
          isPagination={true}
          isCheckbox={true}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetEquipmentInfo;
