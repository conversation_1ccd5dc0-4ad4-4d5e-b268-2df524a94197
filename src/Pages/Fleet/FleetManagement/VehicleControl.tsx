import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useToast } from '@/Common/useToast.tsx';
import EqSettingPopup from '@/Pages/Fleet/Component/FleetManagement/EqSettingPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Switch } from '@/Common/Components/common/Switch';

const VehicleControl = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const [value, setValue] = useState(t('ClusterPassword'));

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [equipmentSelected, setEquipmentSelected] = useState(false);

  const openMEqSettingPopup = () => setIsPopupOpen(true);
  const closePopup = () => setIsPopupOpen(false);
  const confirmEquipment = () => {
    setEquipmentSelected(true);
    closePopup();
  };

  const summaryData = [
    { label: t('Model'), value: '25B-X' },
    { label: t('MachineID'), value: '0010' },
    { label: t('SerialNo'), value: '울산0010' },
    { label: t('Dealership'), value: 'JR SALES & SERVICE' },
  ];

  return (
    <>
      <Tabs.Content value={'VehicleControl'} className={'space-y-10'}>
        {/* 필터 영역 */}
        <article>
          <div className="mb-10 flex items-center justify-between gap-3">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('Model')}</span>
                <Input placeholder={t('Model')} />
              </div>
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('MachineID')}</span>
                <Input placeholder={t('MachineID')} />
              </div>
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('SerialNo')}</span>
                <Input placeholder={t('SerialNo')} />
              </div>
            </div>
            <Button variant={'bt_primary'} label={'Search'} />
          </div>
        </article>

        {/* 장비 설정 영역 */}
        <article>
          <div className="flex gap-2">
            {equipmentSelected && (
              <>
                <Button
                  variant={'bt_primary'}
                  label={'RefreshU'}
                  onClick={() => {
                    toast({
                      description: t('TheConfigurationHasBeenUpdated'),
                      types: 'success',
                    });
                  }}
                />
              </>
            )}
            <Button
              variant={'bt_primary'}
              label={'Send'}
              onClick={() => {
                toast({
                  description: t(
                    'TheConfiguredInformationHasBeenSentToTheRMCU',
                  ),
                  types: 'success',
                });
              }}
            />
          </div>

          {/* 팝업 ConfirmS 클릭 전에는 장비 선택 영역 보임 */}
          {!equipmentSelected && (
            <div className="w-full h-[502px] mt-2 flex items-center justify-center flex-col border border-gray-2">
              <p className="element1-m">
                {t('PleaseSelectTheEquipmentToConfigure')}
              </p>
              <span
                onClick={openMEqSettingPopup}
                className="body1-s text-information underline underline-offset-4 cursor-pointer"
              >
                {t('SelectEquipment')}
              </span>
            </div>
          )}

          {/* 장비 설정 변경 */}
          {equipmentSelected && (
            <div className="mt-3 p-10 bg-white rounded-lg">
              <SummaryData details={summaryData} fs="lg" />

              <div className="divider mt-6 mb-3"></div>

              <Tabs.Root value={value} onValueChange={setValue}>
                <Tabs.List className="tab-design w-[calc(100%+80px)] mx-[-40px]">
                  <Tabs.Trigger value={t('ClusterPassword')}>
                    <span>{t('ClusterPassword')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('OPSS')}>
                    <span>{t('OPSS')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('HAC')}>
                    <span>{t('HAC')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('AutoShift')}>
                    <span>{t('AutoShift')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('ZeroStart')}>
                    <span>{t('ZeroStart')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('DCSR')}>
                    <span>{t('DCSR')}</span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value={t('SpeedLimit')}>
                    <span>{t('SpeedLimit')}</span>
                  </Tabs.Trigger>
                </Tabs.List>

                <div className={'pt-10'}>
                  {/* 클러스터 비밀번호 */}
                  <Tabs.Content
                    value={t('ClusterPassword')}
                    className="flex items-center gap-[74px]"
                  >
                    <span className="body1-b">{t('ClusterPassword')}</span>
                    <Input placeholder={t('ClusterPassword')} />
                  </Tabs.Content>
                  {/* OPSS */}
                  <Tabs.Content value={t('OPSS')}>
                    <div className="h-12 flex items-center gap-[74px]">
                      <span className="body1-b">{t('OPSS')}</span>
                      <Switch label={''} />
                    </div>
                  </Tabs.Content>
                  {/* HAC */}
                  <Tabs.Content value={t('HAC')} className="">
                    <div className="h-12 flex items-center gap-[74px]">
                      <span className="body1-b">{t('HAC')}</span>
                      <Switch label={''} />
                    </div>
                  </Tabs.Content>
                  {/* Auto Shift */}
                  <Tabs.Content
                    value={t('AutoShift')}
                    className="flex items-center gap-[74px]"
                  >
                    <div className="flex items-center gap-[74px]">
                      <span className="body1-b">{t('AutoShift')}</span>
                      <Switch label={''} />
                    </div>
                    <div className="flex items-center gap-8">
                      <div className="flex items-center">
                        <span className="body3-m text-gray-9 mr-5">
                          {t('ShiftUp')}
                        </span>
                        <Input placeholder={t('ShiftUp')} />
                        <span className="body3-m ml-2">{t('Kmh')}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="body3-m text-gray-9 mr-5">
                          {t('ShiftDown')}
                        </span>
                        <Input placeholder={t('ShiftDown')} />
                        <span className="body3-m ml-2">{t('Kmh')}</span>
                      </div>
                    </div>
                  </Tabs.Content>
                  {/* Zero Start */}
                  <Tabs.Content
                    value={t('ZeroStart')}
                    className="flex items-center gap-[74px]"
                  >
                    <div className="flex items-center gap-[74px]">
                      <span className="body1-b">{t('ZeroStart')}</span>
                      <Switch label={''} />
                    </div>
                    <div className="flex items-center">
                      <span className="body3-m text-gray-9 mr-5">
                        {t('ShiftUp')}
                      </span>
                      <Input placeholder={t('ShiftUp')} />
                      <span className="body3-m ml-2">{t('Kmh')}</span>
                    </div>
                  </Tabs.Content>
                  {/* DCSR */}
                  <Tabs.Content
                    value={t('DCSR')}
                    className="flex items-center gap-[74px]"
                  >
                    <div className="flex items-center gap-[74px]">
                      <span className="body1-b">{t('DCSR')}</span>
                      <Switch label={''} />
                    </div>
                    <div className="flex items-center gap-8">
                      <div className="flex items-center">
                        <span className="body3-m text-gray-9 mr-5">
                          {t('ShiftUp')}
                        </span>
                        <Input placeholder={t('ShiftUp')} />
                        <span className="body3-m ml-2">{t('Kmh')}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="body3-m text-gray-9 mr-5">
                          {t('ShiftDown')}
                        </span>
                        <Input placeholder={t('ShiftDown')} />
                        <span className="body3-m ml-2">{t('Kmh')}</span>
                      </div>
                    </div>
                  </Tabs.Content>
                  {/* 제한속도 */}
                  <Tabs.Content
                    value={t('SpeedLimit')}
                    className="flex items-center gap-[74px]"
                  >
                    <div className="flex items-center gap-[74px]">
                      <span className="body1-b">{t('SpeedLimit')}</span>
                      <Switch label={''} />
                    </div>
                    <div className="flex items-center">
                      <span className="body3-m text-gray-9 mr-5">
                        {t('ShiftUp')}
                      </span>
                      <Input placeholder={t('ShiftUp')} />
                      <span className="body3-m ml-2">{t('Kmh')}</span>
                    </div>
                  </Tabs.Content>
                </div>
              </Tabs.Root>
            </div>
          )}
        </article>
      </Tabs.Content>

      {/* 팝업 컴포넌트 렌더링 */}
      {isPopupOpen && (
        <EqSettingPopup
          isOpen={isPopupOpen}
          onClose={closePopup}
          onConfirm={confirmEquipment}
        />
      )}
    </>
  );
};

export default VehicleControl;
