import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { fleetApi } from '@/api';
import { DemoTest } from '@/types';
import { generateFleetSearch } from '@/helpers/fleetDataGenerator';
import { useForm } from 'react-hook-form';

type FleetPageParams = {
  fleetName: string;
  page: number;
  size: number;
  sort: string;
};

type FleetPage = {
  rows: FleetRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type FleetRow = {
  no: number;
  fleetId: number;
  fleetName: string;
  equipmentCount: number;
  driverCount: number;
};

const FleetInfo = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      fleetName: '',
    },
  });

  // 상태 관리
  const [searchTerm, setSearchTerm] = useState('');
  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  /** Params */
  const [fleetPageParams, setFleetPageParams] = useState<
    FleetPageParams | undefined
  >(location.state?.prevParams ?? undefined);

  /** useQuery */
  const { data: fleetPage } = useQuery<FleetPage | null>({
    queryKey: ['/api/fleet/page', fleetPageParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return generateFleetSearch();
      } else {
        try {
          if (fleetPageParams) {
            const response = await fleetApi.getAdminFleetPage(fleetPageParams);
            if (response.data && response.data.content && response.data.page) {
              const result: FleetPage = {
                rows: [],
                page: {
                  pageSize: 0,
                  totalCnt: 0,
                  pageNum: 0,
                },
              };

              response.data.content.forEach((row, index) => {
                if (row.fleetId !== undefined && row.fleetName !== undefined) {
                  result.rows.push({
                    no: index + 1 + fleetPageParams.page * fleetPageParams.size,
                    fleetId: row.fleetId,
                    fleetName: row.fleetName,
                    equipmentCount: row.equipmentCount ?? 0,
                    driverCount: row.driverCount ?? 0,
                  });
                }
                if (response.data.page) {
                  result.page.pageSize =
                    response.data.page.size ?? fleetPageParams.size;
                  result.page.totalCnt = response.data.page.totalElements ?? 0;
                  result.page.pageNum =
                    response.data.page.number ?? fleetPageParams.page;
                }
              });
              return result;
            }
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!fleetPageParams,
  });

  useEffect(() => {
    handleSearch();
  }, []);

  const refreshList = () => {
    resetListCheck();
    setFleetPageParams((prevState) =>
      prevState ? { ...prevState, page: 0, _refresh: Date.now() } : undefined,
    );
  };

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  // 상세페이지 라우팅
  const detailInfo = (rowData: FleetRow) => {
    navigate('/fleet_management/fleetInfoDetails', {
      state: { fleetId: rowData.fleetId, prevParams: fleetPageParams },
    });
  };

  // 셀 클릭 시 상세 페이지 이동
  const detailInfos = ({
    cell,
    row,
  }: {
    cell: { getValue: () => unknown };
    row: { original: FleetRow };
  }) => (
    <span onClick={() => detailInfo(row.original)} className="cursor-pointer">
      {cell.getValue() as string | number}
    </span>
  );

  // 검색 버튼 클릭
  const handleSearch = () => {
    setFleetPageParams({
      fleetName: searchTerm,
      page: 0,
      size: 10,
      sort: 'fleetName,asc',
    });
  };

  // 컬럼 정의
  const columns = [
    {
      header: t('No'),
      accessorKey: 'no',
      cell: detailInfos,
      align: 'left',
      show: true,
    },
    {
      header: t('FleetName'),
      accessorKey: 'fleetName',
      cell: detailInfos,
      align: 'left',
      show: true,
    },
    {
      header: t('Equipment'),
      accessorKey: 'equipmentCount',
      cell: detailInfos,
      show: true,
    },
    {
      header: t('Driver'),
      accessorKey: 'driverCount',
      cell: detailInfos,
      show: true,
    },
  ];

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: FleetRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.fleetId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  // 팝업 함수 더미
  const { openFmDeletePopup, openFmFleetAddPopup } = UseFleetPopup();

  return (
    <Tabs.Content value={'FleetInfo'}>
      {/* 필터 */}
      <form onSubmit={handleSubmit(handleSearch)}>
        <article className="mb-[18px] f-c gap-4">
          <div className="f-c gap-[10px]">
            <Input
              placeholder={t('Fleet')}
              value={searchTerm}
              {...register('fleetName', {
                maxLength: {
                  value: 30,
                  message: 'Maximum 30 characters allowed.',
                },
                pattern: {
                  value: /^[A-Za-z0-9가-힣\s\-_]*$/,
                  message: 'Only English, Korean, and numbers allowed.',
                },
              })}
              error={errors.fleetName?.message}
              onChange={(e) => setSearchTerm(e.target.value)}
              reset={() => setSearchTerm('')}
            />
          </div>
          <Button
            className="self-start"
            type="submit"
            variant={'bt_primary'}
            label={'Search'}
          />
        </article>
      </form>

      {/* 테이블  */}
      <article>
        {/* 테이블 버튼 */}
        <div className="mb-[10px] flex f-je gap-2">
          <Button
            variant={'bt_primary_sm'}
            label={'Delete'}
            onClick={() => openFmDeletePopup(checkedRows, refreshList)}
            disabled={checkedRows.length === 0}
          />
          <Button
            variant={'bt_primary_sm'}
            label={'Add'}
            onClick={() => openFmFleetAddPopup(refreshList)}
          />
        </div>

        <CommonTable
          columns={columns}
          data={fleetPage?.rows ?? []}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
          rowSelection={rowSelection}
          onRowSelectionChange={handleRowSelectionChange}
          isPagination={true}
          customPageSize={fleetPage?.page.pageSize ?? 0}
          totalCount={fleetPage?.page.totalCnt ?? 0}
          currentPage={fleetPage?.page.pageNum ? fleetPage.page.pageNum + 1 : 1}
          onPageChange={(page: number) => {
            setFleetPageParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
            resetListCheck();
          }}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetInfo;
