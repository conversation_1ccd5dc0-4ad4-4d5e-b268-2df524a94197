import { useTranslation } from 'react-i18next';
import { ChangeEvent, useState } from 'react';
import { Tabs } from '@radix-ui/themes';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Tooltip, Badge } from '@radix-ui/themes';

const FleetFaultOccurrence = () => {
  const { t } = useTranslation();

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);

  const handleSelectionChange = (selectedRows: Record<string, unknown>[]) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => String(row.model ?? '0')));
    }
  };

  // 모든 API 데이터 삭제, 목데이터로 대체
  const areaSelectData: { key: string; value: string }[] = [
    { key: '전체', value: 'ALL' },
  ];
  const countrySelectData: { key: string; value: string }[] = [
    { key: '전체', value: 'ALL' },
  ];
  const fleetEquipmentData: { key: string; value: string }[] = [
    { key: '전체', value: 'ALL' },
  ];
  const fleetDealerData: { key: string; value: string }[] = [
    { key: '전체', value: 'ALL' },
  ];

  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 현재 날짜 기준으로 1개월 전 날짜 계산
  const today = new Date();
  const oneMonthAgo = new Date();

  // 날짜 포맷 함수 (YYYY-MM-DD)
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 입력 필드 값을 저장할 상태
  const [inputValues, setInputValues] = useState({
    fleetName: '',
    dealer: '',
    model: '',
    hogi: '',
    country: '',
    place: '',
    level: 'ALL',
    alarmType: 'ALL',
    custumNo: '',
    description: '',
    startDate: formatDate(oneMonthAgo),
    endDate: formatDate(today),
  });

  // API 파라미터 관련 코드 모두 삭제
  const [requestParams, setRequestParams] = useState({
    pageNum: 1,
    pageSize: pageSize,
    langType: 'KR',
    level: 'ALL',
    alarmType: 'ALL',
    startDate: formatDate(oneMonthAgo),
    endDate: formatDate(today),
  });

  // 테이블 목 데이터
  const FleetFaultOccurrenceData = {
    breakdownList: [] as Record<string, unknown>[],
  };

  // 검색 조건 변경 함수 (실제로는 setState만 동작)
  const updateSearchParams = (
    field: string,
    value: string | number | boolean,
  ) => {
    setRequestParams((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    updateSearchParams('pageNum', newPage);
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === 'country') {
      setInputValues((prev) => ({
        ...prev,
        place: '',
      }));
    }
  };

  // 날짜 범위 변경 핸들러
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    const formattedStartDate = formatDate(new Date(startDate));
    const formattedEndDate = formatDate(new Date(endDate));
    setInputValues((prev) => ({
      ...prev,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    }));
  };

  // 수동 검색 버튼 핸들러
  const handleSearch = () => {
    // do nothing, 파라미터만 세팅
    setRequestParams((prev) => ({
      ...prev,
      ...inputValues,
      pageNum: 1,
    }));
  };

  // 초기화 버튼 핸들러
  const resetModelInput = () => {
    setInputValues((prev) => ({
      ...prev,
      model: '',
    }));
  };

  const resetHogiInput = () => {
    setInputValues((prev) => ({
      ...prev,
      hogi: '',
    }));
  };

  const resetCustumNoInput = () => {
    setInputValues((prev) => ({
      ...prev,
      custumNo: '',
    }));
  };

  const resetDescriptionInput = () => {
    setInputValues((prev) => ({
      ...prev,
      description: '',
    }));
  };

  // 상세/기본 필터 토글
  const [isDetailedFilter, setIsDetailedFilter] = useState(false);

  // 드롭다운 옵션 (enum 없음)
  const options1 = [
    { key: t('All'), value: 'ALL' },
    { key: t('Warning2'), value: 'Caution' },
    { key: t('ServiceSoon'), value: 'ImmediateService' },
    { key: t('ServiceNow'), value: 'Service' },
    { key: t('StopSoon'), value: 'ImmediateStop' },
    { key: t('StopNow'), value: 'Stop' },
  ];

  const options2 = [
    { key: t('All'), value: 'ALL' },
    { key: 'A', value: 'A' },
    { key: 'B', value: 'B' },
    { key: 'F', value: 'F' },
    { key: 'G', value: 'G' },
    { key: 'I', value: 'I' },
    { key: 'L', value: 'L' },
    { key: 'O', value: 'O' },
    { key: 'S', value: 'S' },
    { key: 'W', value: 'W' },
  ];

  const columns = [
    {
      header: t('Date'),
      accessorKey: 'regDt',
    },
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'equipmentId',
    },
    {
      header: t('Mileage'),
      accessorKey: 'mileage',
    },
    {
      header: t('AlarmType'),
      accessorKey: 'alarmType',
      cell: ({ row }: { row: { original: Record<string, unknown> } }) => {
        const alarmType = row.original.alarmType || 'N/A';
        return (
          <div className="flex justify-center">
            <Tooltip content="전후방 기준: 8G 측면 기준: 8G 상하 기준: 8G">
              <Badge
                color="gray"
                variant="soft"
                radius="full"
                className="body3-m flex justify-center w-7 h-7"
              >
                {String(alarmType)}
              </Badge>
            </Tooltip>
          </div>
        );
      },
    },
    {
      header: t('Severity'),
      accessorKey: 'level',
    },
    {
      header: t('Description'),
      accessorKey: 'description',
    },
    {
      header: t('Guide'),
      accessorKey: 'place',
      cell: () => <span className="blue-underline">{t('View')}</span>,
    },
    {
      header: t('MessageSend'),
      accessorKey: 'equipmentId',
      cell: () => <Button variant={'bt_primary'} label={'Forwarding'} />,
    },
    {
      header: t('ReceiptStatus'),
      accessorKey: 'receipt',
      cell: ({ row }: { row: { original: Record<string, unknown> } }) => {
        const alarmType = !!row.original.receipt;
        return (
          <div className="flex justify-center">
            <span>{alarmType ? t('Receipt') : t('NotReceived')}</span>
          </div>
        );
      },
    },
  ];

  return (
    <Tabs.Content value={'CurrentFault'}>
      {/* 필터 영역 */}
      <article>
        <div className="flex flex-col">
          {/* 필터 */}
          <div
            className={`mb-10 flex ${isDetailedFilter ? 'items-end' : 'items-start'} justify-between gap-6`}
          >
            <div
              className={`flex ${isDetailedFilter ? 'items-end' : 'items-start'} justify-between gap-6`}
            >
              <div className="flex flex-col gap-5">
                {/* 기본 필터 영역 */}
                <div className="flex gap-6">
                  <SearchItemContainer>
                    <SearchLabel>{t('Fleet')}</SearchLabel>
                    <DropDown
                      onChange={(value) =>
                        handleDropdownChange('fleetName', value.toString())
                      }
                      options={fleetEquipmentData}
                      placeholder={t('All')}
                    />
                  </SearchItemContainer>
                  <SearchItemContainer>
                    <SearchLabel>{t('DealerD')}</SearchLabel>
                    <DropDown
                      onChange={(value) =>
                        handleDropdownChange('dealer', value.toString())
                      }
                      options={fleetDealerData}
                      placeholder={t('All')}
                    />
                  </SearchItemContainer>
                  <div className="flex items-center gap-6">
                    <span className="body1-b whitespace-nowrap">
                      {t('Model')}
                    </span>
                    <Input
                      placeholder={t('Model')}
                      className="w-[144px]"
                      name="model"
                      value={inputValues.model}
                      onChange={handleInputChange}
                      reset={resetModelInput}
                    />
                  </div>
                  <SearchItemContainer>
                    <SearchLabel>{t('Date')}</SearchLabel>
                    <FromToSelector
                      onChange={(startDate, endDate) =>
                        handleDateRangeChange(startDate, endDate)
                      }
                    />
                  </SearchItemContainer>
                </div>
                {/* 상세 필터 영역 */}
                {isDetailedFilter && (
                  <div className="flex flex-col gap-5">
                    {/* 상세 필터 첫 번째 줄 */}
                    <div className="flex gap-6">
                      <SearchItemContainer>
                        <SearchLabel>{t('Country')}</SearchLabel>
                        <DropDown
                          onChange={(value) =>
                            handleDropdownChange('country', value.toString())
                          }
                          options={areaSelectData}
                          placeholder={t('All')}
                        />
                      </SearchItemContainer>
                      <SearchItemContainer>
                        <SearchLabel>{t('Region')}</SearchLabel>
                        <DropDown
                          onChange={(value) =>
                            handleDropdownChange('place', value.toString())
                          }
                          options={countrySelectData}
                          placeholder={t('All')}
                        />
                      </SearchItemContainer>
                      <SearchItemContainer>
                        <SearchLabel>{t('Severity')}</SearchLabel>
                        <DropDown
                          onChange={(value) =>
                            handleDropdownChange('level', value.toString())
                          }
                          options={options1}
                          placeholder={t('All')}
                        />
                      </SearchItemContainer>
                      <SearchItemContainer>
                        <SearchLabel>{t('AlarmType')}</SearchLabel>
                        <DropDown
                          onChange={(value) =>
                            handleDropdownChange('alarmType', value.toString())
                          }
                          options={options2}
                          placeholder={t('All')}
                        />
                      </SearchItemContainer>
                    </div>
                    {/* 상세 필터 두 번째 줄 */}
                    <div className="flex gap-6">
                      <div className="flex items-center gap-6">
                        <span className="body1-b whitespace-nowrap">
                          {t('MachineID')}
                        </span>
                        <Input
                          placeholder={t('MachineID')}
                          name="hogi"
                          value={inputValues.hogi}
                          onChange={handleInputChange}
                          reset={resetHogiInput}
                        />
                      </div>
                      <div className="flex items-center gap-6">
                        <span className="body1-b whitespace-nowrap">
                          {t('SerialNo')}
                        </span>
                        <Input
                          placeholder={t('SerialNo')}
                          name="custumNo"
                          value={inputValues.custumNo}
                          onChange={handleInputChange}
                          reset={resetCustumNoInput}
                        />
                      </div>
                      <div className="flex items-center gap-6">
                        <span className="body1-b whitespace-nowrap">
                          {t('Description')}
                        </span>
                        <Input
                          placeholder={t('Description')}
                          className="w-full sm:w-[146px]"
                          name="description"
                          value={inputValues.description}
                          onChange={handleInputChange}
                          reset={resetDescriptionInput}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <Button
                variant={'bt_primary'}
                label={'AdvancedFilter'}
                onClick={() => setIsDetailedFilter(!isDetailedFilter)}
                className="w-fit px-4 text-gray-15 bg-transparent border border-[#D9D9D9] flex items-center gap-1 [&_path]:text-secondary-2"
              />
            </div>
            <div className="flex gap-3">
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
              />
              <Button variant={'bt_primary'} label={'Print'} />
            </div>
          </div>
          {/* 버튼 영역 */}
          <div className="mb-3 flex gap-2">
            <Button
              variant={'bt_primary'}
              label={'MpV'}
              onClick={() => {}}
              disabled={selectedCheck.length === 0}
            />
            <Button
              variant={'bt_primary'}
              label={'OperationLimitaion'}
              disabled={selectedCheck.length === 0}
              className="flex items-center gap-1"
            />
          </div>
        </div>
      </article>
      {/* 테이블 영역 */}
      <article>
        <CommonTable
          columns={columns}
          data={FleetFaultOccurrenceData.breakdownList}
          isPagination={true}
          isCheckbox={true}
          customPageSize={pageSize}
          currentPage={requestParams.pageNum}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          onSelectionChange={handleSelectionChange}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetFaultOccurrence;
