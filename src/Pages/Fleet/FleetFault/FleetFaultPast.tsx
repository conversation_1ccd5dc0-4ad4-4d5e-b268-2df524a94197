import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import Input from '@/Common/Components/common/Input';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Tooltip, Badge } from '@radix-ui/themes';
import { ChangeEvent, useState, useMemo } from 'react';

interface FleetPastBreakdownInfo {
  regDt?: string;
  responseDt?: string;
  model?: string;
  equipmentId?: string;
  mileage?: string;
  alarmType?: string;
  level?: string;
  description?: string;
  regId?: number;
}

interface FleetFaultPageDTO {
  totalPage: number;
  breakdownList: FleetPastBreakdownInfo[];
}

interface FleetFaultApiGetFleetPastBreakdownPageRequest {
  pageNum: number;
  pageSize: number;
  langType: string;
  level: string;
  alarmType: string;
  startDate: string;
  endDate: string;
  dateCondition: number;
  model?: string;
  hogi?: string;
  country?: string;
  place?: string;
  custumNo?: string;
}

enum GetFleetFaultPageLevelEnum {
  All = 'ALL',
  High = 'HIGH',
  Medium = 'MEDIUM',
  Low = 'LOW',
}

enum GetFleetFaultPageAlarmTypeEnum {
  All = 'ALL',
  Engine = 'ENGINE',
  Hydraulic = 'HYDRAULIC',
  Electric = 'ELECTRIC',
}

enum GetFleetFaultPageLangTypeEnum {
  Kr = 'KR',
  En = 'EN',
}

const FleetFaultPast = () => {
  const { t } = useTranslation();

  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(25); // Mock total count

  // 현재 날짜 기준으로 당일 날짜 계산
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(today.getMonth() - 1);

  // 날짜 포맷 함수 (YYYY-MM-DD)
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 입력 필드 값을 저장할 상태
  const [inputValues, setInputValues] = useState({
    model: '',
    hogi: '',
    country: '',
    place: '',
    level: GetFleetFaultPageLevelEnum.All,
    alarmType: GetFleetFaultPageAlarmTypeEnum.All,
    custumNo: '',
    description: '',
    startDate: formatDate(oneMonthAgo),
    endDate: formatDate(today),
    dateCondition: '0',
  });

  const [requestParams, setRequestParams] =
    useState<FleetFaultApiGetFleetPastBreakdownPageRequest>({
      pageNum: 1,
      pageSize: pageSize,
      langType: GetFleetFaultPageLangTypeEnum.Kr,
      level: GetFleetFaultPageLevelEnum.All,
      alarmType: GetFleetFaultPageAlarmTypeEnum.All,
      startDate: formatDate(oneMonthAgo),
      endDate: formatDate(today),
      dateCondition: 0,
    });

  // Mock 고장 데이터
  const mockBreakdownData: FleetPastBreakdownInfo[] = useMemo(
    () => [
      {
        regDt: '2024-01-15 09:30:00',
        responseDt: '2024-01-15 14:20:00',
        model: 'EX120-5',
        equipmentId: 'EQ001',
        mileage: '1250',
        alarmType: 'E',
        level: 'HIGH',
        description: 'Engine overheating detected',
        regId: 1,
      },
      {
        regDt: '2024-01-20 11:15:00',
        responseDt: '2024-01-20 16:45:00',
        model: 'EX200-6',
        equipmentId: 'EQ002',
        mileage: '2350',
        alarmType: 'H',
        level: 'MEDIUM',
        description: 'Hydraulic pressure drop',
        regId: 2,
      },
      {
        regDt: '2024-02-01 08:45:00',
        responseDt: '2024-02-01 12:30:00',
        model: 'DX140LC-5',
        equipmentId: 'EQ003',
        mileage: '890',
        alarmType: 'E',
        level: 'LOW',
        description: 'Battery voltage low',
        regId: 3,
      },
      {
        regDt: '2024-02-10 14:20:00',
        responseDt: '2024-02-10 18:10:00',
        model: 'EX300-7',
        equipmentId: 'EQ004',
        mileage: '3250',
        alarmType: 'H',
        level: 'HIGH',
        description: 'Hydraulic filter clogged',
        regId: 4,
      },
      {
        regDt: '2024-02-15 10:30:00',
        responseDt: '2024-02-15 15:20:00',
        model: 'EX450-8',
        equipmentId: 'EQ005',
        mileage: '1890',
        alarmType: 'E',
        level: 'MEDIUM',
        description: 'Engine temperature sensor fault',
        regId: 5,
      },
    ],
    [],
  );

  const FleetFaultOccurrenceData: FleetFaultPageDTO = useMemo(
    () => ({
      totalPage: Math.ceil(mockBreakdownData.length / pageSize),
      breakdownList: mockBreakdownData,
    }),
    [mockBreakdownData, pageSize],
  );

  // 검색 조건 변경 함수
  const updateSearchParams = (
    field: keyof FleetFaultApiGetFleetPastBreakdownPageRequest,
    value: string | number | boolean,
  ) => {
    setRequestParams((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    updateSearchParams('pageNum', newPage);
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 날짜 범위 변경 핸들러
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    const formattedStartDate = formatDate(new Date(startDate));
    const formattedEndDate = formatDate(new Date(endDate));
    console.log(formattedStartDate);
    console.log(formattedEndDate);

    setInputValues((prev) => ({
      ...prev,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    }));
  };

  // 수동 검색 버튼 핸들러
  const handleSearch = async () => {
    try {
      // 페이지 번호 초기화 및 파라미터 설정
      const newParams: FleetFaultApiGetFleetPastBreakdownPageRequest = {
        ...requestParams,
        pageNum: 1,
        model: inputValues.model || undefined,
        hogi: inputValues.hogi || undefined,
        country: inputValues.country || undefined,
        place: inputValues.place || undefined,
        level: inputValues.level as GetFleetFaultPageLevelEnum,
        alarmType: inputValues.alarmType as GetFleetFaultPageAlarmTypeEnum,
        custumNo: inputValues.custumNo || undefined,
        startDate: inputValues.startDate,
        endDate: inputValues.endDate,
        dateCondition: Number(inputValues.dateCondition) || 0,
      };
      setRequestParams(newParams);
      console.log('Search parameters:', newParams);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  // 초기화 버튼 핸들러
  const resetModelInput = () => {
    setInputValues((prev) => ({
      ...prev,
      model: '',
    }));
  };

  const dateConditionItem = [
    { key: '고장 발생일', value: '0' },
    { key: '고장 소거일', value: '1' },
  ];

  const columns = [
    {
      header: t('FaultOccurrenceDate'),
      accessorKey: 'regDt',
    },
    {
      header: t('FaultResolutionDate'),
      accessorKey: 'responseDt',
    },
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'equipmentId',
    },
    {
      header: t('Mileage'),
      accessorKey: 'mileage',
    },
    {
      header: t('AlarmType'),
      accessorKey: 'alarmType',
      cell: ({ row }: { row: { original: FleetPastBreakdownInfo } }) => {
        const alarmType = row.original.alarmType || 'N/A';

        return (
          <div className="flex justify-center">
            <Tooltip content={t('FaultE')}>
              <Badge
                color="gray"
                variant="soft"
                radius="full"
                className="body3-m flex justify-center w-7 h-7"
              >
                {alarmType}
              </Badge>
            </Tooltip>
          </div>
        );
      },
    },
    {
      header: t('Severity'),
      accessorKey: 'level',
    },
    {
      header: t('Description'),
      accessorKey: 'description',
    },
    {
      header: t('History'),
      accessorKey: 'regId',
    },
  ];

  return (
    <Tabs.Content value={'PastFault'} className="space-y-6 md:space-y-10">
      {/* 필터 */}
      <article>
        <div className="flex items-center justify-between">
          <div className="flex gap-6">
            <div className="flex items-center gap-6">
              <span className="body1-b whitespace-nowrap">{t('Model')}</span>
              <Input
                placeholder={t('Model')}
                className="w-[144px]"
                name="model"
                value={inputValues.model}
                onChange={handleInputChange}
                reset={resetModelInput}
              />
            </div>

            <SearchItemContainer>
              <SearchLabel>{t('DateCriteria')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('dateCondition', value.toString())
                }
                options={dateConditionItem || []}
                placeholder={t('FaultOccurrenceDate')}
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <SearchLabel>{t('Date')}</SearchLabel>
              <FromToSelector
                onChange={(startDate, endDate) =>
                  handleDateRangeChange(startDate, endDate)
                }
              />
            </SearchItemContainer>
          </div>

          <div className="flex gap-3">
            <Button
              variant={'bt_primary'}
              label={'Search'}
              onClick={handleSearch}
            />
            <Button variant={'bt_primary'} label={'aaPrinta'} />
          </div>
        </div>
      </article>

      {/* 테이블 */}
      <article>
        <CommonTable
          columns={columns}
          data={FleetFaultOccurrenceData?.breakdownList || []}
          isPagination={true}
          isCheckbox={false}
          customPageSize={pageSize}
          currentPage={requestParams.pageNum}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetFaultPast;
