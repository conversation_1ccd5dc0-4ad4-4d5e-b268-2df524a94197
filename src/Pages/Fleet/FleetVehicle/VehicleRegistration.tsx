import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useDistanceUnit } from '@/context/DistanceUnitContext.tsx';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import YearSelector from '@/Common/Components/datePicker/YearSelector';
import DropDownPaginated from '@/Common/Components/common/DropDownPaginated';
import CheckBox from '@/Common/Components/common/CheckBox';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  equipmentApi,
  fleetApi,
  dealerApi,
  serviceCenterApi,
  enumApi,
} from '@/api';
import dayjs from 'dayjs';
import { DemoTest, DropdownOption, EnumItemType } from '@/types';
import { useCallback, useEffect, useState } from 'react';
import {
  AdminEquipmentApiCreateAdminEquipmentRequest,
  AdminEquipmentCreateReqDTOEquipmentTypeEnum,
  AdminEquipmentCreateReqDTOFuelTypeEnum,
  AdminEquipmentCreateReqDTOVehicleBodyClassEnum,
  AdminEquipmentCreateReqDTOVehicleTypeEnum,
} from '@/api/generated';
import { useQuery } from '@tanstack/react-query';

const schema = yup.object().shape({
  //BasicInformation
  vinNumber: yup
    .string()
    .required('VIN number is required.')
    .matches(
      /^[A-HJ-NPR-Z0-9]{1,17}$/,
      'Only uppercase letters and numbers (max 17 chars).',
    ), //차대 번호
  manufacturer: yup
    .string()
    .required('Manufacturer is required.')
    .max(50, 'Max 50 characters.'), //제조사 이름
  modelName: yup
    .string()
    .required('Model name is required.')
    .max(50, 'Max 50 characters.'), //모델 이름
  trimName: yup
    .string()
    .required('Trim name is required.')
    .max(50, 'Max 50 characters.'), //트림 이름
  yearOfManufacture: yup
    .string()
    .required('Year of manufacture is required.')
    .matches(/^[0-9]{4}$/, 'Year must be 4 digits.'), //제조 연도
  plateNo: yup
    .string()
    .required('Vehicle number is required.')
    .max(20, 'Max 20 characters.'), //차량 번호
  //VehicleInformation
  vehicleType: yup
    .object({
      key: yup.string(),
      value: yup.string(),
    })
    .test('required-vehicleType', 'Vehicle type is required.', (value) => {
      return !!value?.key;
    }), //트럭 타입
  bodyClass: yup.object({
    key: yup.string(),
    value: yup.string(),
  }), //차량 종류
  lengthKm: yup.string().when('$distanceUnit', {
    is: 'km',
    then: (s) =>
      s.required('Length is required.').matches(/^[0-9]+$/, 'Numbers only.'),
  }), //길이(Km)
  lengthFt: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Length (Ft) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //길이(Mile, Feet)
  lengthIn: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Length (In) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //길이(Mile, Inch)
  heightKm: yup.string().when('$distanceUnit', {
    is: 'km',
    then: (s) =>
      s.required('Height is required.').matches(/^[0-9]+$/, 'Numbers only.'),
  }), //높이(Km)
  heightFt: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Height (Ft) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //높이(Mile, Feet)
  heightIn: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Height (In) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //높이(Mile, Inch)
  widthKm: yup.string().when('$distanceUnit', {
    is: 'km',
    then: (s) =>
      s.required('Width is required.').matches(/^[0-9]+$/, 'Numbers only.'),
  }), //너비(Km)
  widthFt: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Width (Ft) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //너비(Mile, Feet)
  widthIn: yup.string().when('$distanceUnit', {
    is: 'mi',
    then: (s) =>
      s
        .required('Width (In) is required.')
        .matches(/^[0-9]+$/, 'Numbers only.'),
  }), //너비(Mile, Inch)
  hazardousMaterials: yup.array().of(yup.string()), //위험물질
  //EfficiencyInformation
  fuelType: yup
    .object({
      key: yup.string(),
      value: yup.string(),
    })
    .test('required-fuelType', 'Fuel type is required.', (value) => {
      return !!value?.key;
    }), //연료 타입
  fuelEfficiency: yup.string().matches(/^[0-9.]*$/, 'Numbers only'), //연비
  fuelEfficiencyUnit: yup
    .object({
      key: yup.string(),
      value: yup.string(),
    })
    .test(
      'required-fuelEfficiencyUnit',
      'Fuel efficiency unit is required.',
      (value) => {
        return !!value?.key;
      },
    ), //연비 단위
  fuelTankCapacity: yup.string().matches(/^[0-9.]*$/, 'Numbers only'), //연료 탱크 용량
  fuelTankCapacityUnit: yup
    .object({
      key: yup.string(),
      value: yup.string(),
    })
    .test(
      'required-fuelTankCapacityUnit',
      'Fuel tank capacity unit is required.',
      (value) => {
        return !!value?.key;
      },
    ), //연료 탱크 용량 단위
  tireDiameter: yup.string().matches(/^[0-9.]*$/, 'Numbers only'), //타이어 직경
  tireWidth: yup.string().matches(/^[0-9.]*$/, 'Numbers only'), //타이어 너비
  //ConsumableInformation
  consumableUnit: yup.string().oneOf(['km', 'mi']).required(), //단위
  consumableItems: yup.array().of(
    yup.object({
      key: yup.string(), //소모품 항목
      value: yup.number(), //교체 기준
    }),
  ),
  //FleetInformation
  fleet: yup.object({ key: yup.string(), value: yup.string() }).nullable(),
  //DealerInformation
  dealer: yup.object({ key: yup.string(), value: yup.string() }).nullable(),
  //ServiceCenterInformation
  serviceCenter: yup
    .object({ key: yup.string(), value: yup.string() })
    .nullable(),
});

type VehicleFormData = yup.InferType<typeof schema>;

const VehicleRegistration = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { openFvBackPopup } = UseFleetPopup();
  const { distanceUnit, setDistanceUnit } = useDistanceUnit();
  const [isVinValid, setIsVinValid] = useState<boolean>(false);

  const { data: vehicleTypeOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'vehicleType', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: t('Car'), value: 'CAR' },
          { key: t('SuvRv'), value: 'SUV_RV' },
          { key: t('Truck'), value: 'TRUCK' },
          { key: t('Bus'), value: 'BUS' },
          { key: t('Trailer'), value: 'TRAILER' },
          { key: t('Incomplete'), value: 'INCOMPLETE' },
          { key: t('LowSpeed'), value: 'LOW_SPEED' },
          { key: t('Bike'), value: 'BIKE' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'vehicleType',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const { data: bodyClassOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'vehicleBodyClass', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: t('Sedan'), value: 'SEDAN' },
          { key: t('Hatchback'), value: 'HATCHBACK' },
          { key: t('Coupe'), value: 'COUPE' },
          { key: t('Convertible'), value: 'CONVERTIBLE' },
          { key: t('Suv'), value: 'SUV' },
          { key: t('Van'), value: 'VAN' },
          { key: t('Minivan'), value: 'MINIVAN' },
          { key: t('Wagon'), value: 'WAGON' },
          { key: t('PickupTruck'), value: 'PICKUP_TRUCK' },
          { key: t('Bus'), value: 'BUS' },
          { key: t('Minibus'), value: 'MINIBUS' },
          { key: t('HeavyTruck'), value: 'HEAVY_TRUCK' },
          { key: t('Trike'), value: 'TRIKE' },
          { key: t('OffRoad'), value: 'OFF_ROAD' },
          { key: t('Incomplete'), value: 'INCOMPLETE' },
          { key: t('Trailer'), value: 'TRAILER' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'vehicleBodyClass',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const hazardousMaterialsList = [
    'Explosives',
    'Gas',
    'Flammable',
    'Organic',
    'Poison',
    'Radioactive',
    'Corrosive',
    'HarmfulForWater',
    'PoisonousInhalationHazard',
    'Other',
  ];

  const fuelTypeOptions = [
    { key: t('Diesel'), value: 'DIESEL' },
    { key: t('Gasoline'), value: 'GASOLINE' },
    { key: t('Hybrid'), value: 'HYBRID' },
    { key: t('Electric'), value: 'ELECTRIC' },
  ];

  const fuelEfficiencyUnitOptions = [
    { key: 'MPG', value: 'MPG' },
    { key: 'Km/L', value: 'Km/L' },
  ];

  const fuelTankCapacityUnitOptions = [
    { key: 'L', value: 'L' },
    { key: 'Gal', value: 'Gal' },
  ];

  const consumableHideKeysForElectric = [
    'EngineOil',
    'OilFilter',
    'FuelFilter',
    'Coolant',
    'TransmissionOil',
  ];

  const defaultConsumableItems = {
    km: [
      { key: 'EngineOil', value: 25000 },
      { key: 'OilFilter', value: 25000 },
      { key: 'FuelFilter', value: 40000 },
      { key: 'AirFilter', value: 40000 },
      { key: 'BrakePads', value: 70000 },
      { key: 'BrakeLining', value: 80000 },
      { key: 'Tires', value: 100000 },
      { key: 'Coolant', value: 2 },
      { key: 'Battery', value: 2 },
      { key: 'TransmissionOil', value: 100000 },
      { key: 'TireRotation', value: 0 },
    ],
    mi: [
      { key: 'EngineOil', value: 15534 },
      { key: 'OilFilter', value: 15534 },
      { key: 'FuelFilter', value: 24855 },
      { key: 'AirFilter', value: 24855 },
      { key: 'BrakePads', value: 43496 },
      { key: 'BrakeLining', value: 49710 },
      { key: 'Tires', value: 62137 },
      { key: 'Coolant', value: 2 },
      { key: 'Battery', value: 2 },
      { key: 'TransmissionOil', value: 62137 },
      { key: 'TireRotation', value: 0 },
    ],
  };

  type FormValues = yup.InferType<typeof schema>;

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    trigger,
    setError,
    clearErrors,
    formState: { errors, isValid },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    context: { distanceUnit },
    mode: 'onChange',
    defaultValues: {
      vinNumber: '',
      manufacturer: '',
      modelName: '',
      trimName: '',
      yearOfManufacture: dayjs().format('YYYY'),
      plateNo: '',
      vehicleType: { key: '', value: '' },
      bodyClass: { key: '', value: '' },
      lengthKm: '',
      lengthFt: '',
      lengthIn: '',
      heightKm: '',
      heightFt: '',
      heightIn: '',
      widthKm: '',
      widthFt: '',
      widthIn: '',
      hazardousMaterials: [],
      fuelType: { key: '', value: '' },
      fuelEfficiency: '',
      fuelEfficiencyUnit: { key: 'MPG', value: 'MPG' },
      fuelTankCapacity: '',
      fuelTankCapacityUnit: { key: 'Gal', value: 'Gal' },
      tireDiameter: '',
      tireWidth: '',
      consumableUnit: 'mi',
      consumableItems: defaultConsumableItems.mi,
      fleet: { key: '', value: '' },
      dealer: { key: '', value: '' },
      serviceCenter: { key: '', value: '' },
    },
  });

  const { fields, replace } = useFieldArray({
    control,
    name: 'consumableItems',
  });

  const vinNumber = watch('vinNumber');
  useEffect(() => {
    setIsVinValid(false); // vinNumber 변경 시 검증 상태 초기화
    clearErrors('vinNumber'); // 기존 에러 초기화
  }, [vinNumber, clearErrors]);

  const pageLeave = () => {
    openFvBackPopup(() => {
      navigate(-1);
    });
  };

  const handleInspectVin = async () => {
    try {
      if (!vinNumber) {
        setError('vinNumber', {
          type: 'manual',
          message: 'Please enter a VIN number.',
        });
        setIsVinValid(false);
        return;
      }

      const response = await equipmentApi.lookupAdminEquipmentSerialNo({
        serialNo: vinNumber,
      });

      if (response.data) {
        // 이미 등록된 VIN
        setError('vinNumber', {
          type: 'manual',
          message: 'This VIN is already registered.',
        });
        setIsVinValid(false);
      } else {
        // 사용 가능한 VIN
        clearErrors('vinNumber');
        setIsVinValid(true);
        await trigger();
        alert('This VIN number is available.');
      }
    } catch {
      setError('vinNumber', {
        type: 'manual',
        message: 'Error while checking VIN number.',
      });
      setIsVinValid(false);
    }
  };

  const handleRegister = async (data: VehicleFormData) => {
    if (!isVinValid) {
      setError('vinNumber', {
        type: 'manual',
        message: 'VIN number must be verified.',
      });
      alert('Please verify the VIN number.');
      return;
    }

    if (!isValid) {
      const errorMessages = Object.values(errors)
        .map((error) => error?.message)
        .filter(Boolean)
        .join('\n');

      alert(`유효성 검사 실패:\n${errorMessages}`);
      return;
    }

    const requestBody: AdminEquipmentApiCreateAdminEquipmentRequest = {
      adminEquipmentCreateReqDTO: [
        {
          countryId: undefined, //국가아이디
          dealerId: data.dealer?.value ? Number(data.dealer.value) : undefined, //딜러아이디
          serviceCenterId: data.serviceCenter?.value
            ? Number(data.serviceCenter.value)
            : undefined, //서비스센터아이디
          fleetIdList: data.fleet?.value ? [Number(data.fleet.value)] : [], //플릿아이디목록

          equipmentType: AdminEquipmentCreateReqDTOEquipmentTypeEnum.Vehicle,
          vehicleType: data.vehicleType
            .value as AdminEquipmentCreateReqDTOVehicleTypeEnum,
          vehicleBodyClass: data.bodyClass
            .value as AdminEquipmentCreateReqDTOVehicleBodyClassEnum,

          bodyLength:
            distanceUnit === 'km'
              ? Number(data.lengthKm || 0)
              : Number(data.lengthFt || 0),
          bodyHeight:
            distanceUnit === 'km'
              ? Number(data.heightKm || 0)
              : Number(data.heightFt || 0),
          bodyWidth:
            distanceUnit === 'km'
              ? Number(data.widthKm || 0)
              : Number(data.widthFt || 0),

          hazmatExplosives: data.hazardousMaterials?.includes('Explosives'),
          hazmatGas: data.hazardousMaterials?.includes('Gas'),
          hazmatFlammable: data.hazardousMaterials?.includes('Flammable'),
          hazmatOrganic: data.hazardousMaterials?.includes('Organic'),
          hazmatPoison: data.hazardousMaterials?.includes('Poison'),
          hazmatRadioactive: data.hazardousMaterials?.includes('Radioactive'),
          hazmatCorrosive: data.hazardousMaterials?.includes('Corrosive'),
          hazmatHarmfulForWater:
            data.hazardousMaterials?.includes('HarmfulForWater'),
          hazmatPoisonousInhalationHazard: data.hazardousMaterials?.includes(
            'PoisonousInhalationHazard',
          ),
          hazmatOther: data.hazardousMaterials?.includes('Other'),

          fuelType: data.fuelType
            .value as AdminEquipmentCreateReqDTOFuelTypeEnum,
          fuelEfficiency: Number(data.fuelEfficiency || 0),
          fuelTankCapacity: Number(data.fuelTankCapacity || 0),
          tireDiameter: Number(data.tireDiameter || 0),
          tireWidth: Number(data.tireWidth || 0),

          engineOilInterval:
            data.consumableItems?.find((idx) => idx.key === 'EngineOil')
              ?.value || 0,
          oilFilterInterval:
            data.consumableItems?.find((idx) => idx.key === 'OilFilter')
              ?.value || 0,
          fuelFilterInterval:
            data.consumableItems?.find((idx) => idx.key === 'FuelFilter')
              ?.value || 0,
          airFilterInterval:
            data.consumableItems?.find((idx) => idx.key === 'AirFilter')
              ?.value || 0,
          brakePadInterval:
            data.consumableItems?.find((idx) => idx.key === 'BrakePads')
              ?.value || 0,
          brakeLiningInterval:
            data.consumableItems?.find((idx) => idx.key === 'BrakeLining')
              ?.value || 0,
          tireInterval:
            data.consumableItems?.find((idx) => idx.key === 'Tires')?.value ||
            0,
          coolantInterval:
            data.consumableItems?.find((idx) => idx.key === 'Coolant')?.value ||
            0,
          batteryInterval:
            data.consumableItems?.find((idx) => idx.key === 'Battery')?.value ||
            0,
          transmissionOilInterval:
            data.consumableItems?.find((idx) => idx.key === 'TransmissionOil')
              ?.value || 0,
          tireRotationInterval:
            data.consumableItems?.find((idx) => idx.key === 'TireRotation')
              ?.value || 0,

          manufacturer: data.manufacturer,
          modelName: data.modelName,
          trimName: data.trimName,
          productYear: Number(data.yearOfManufacture),
          imagePath: undefined,
          serialNo: data.vinNumber,
          plateNo: data.plateNo,
        },
      ],
    };

    try {
      await equipmentApi.createAdminEquipment(requestBody);
      alert('Registration successful!');
    } catch (error) {
      console.error(error);
      alert('Failed to register equipment.');
    }
  };

  const fleetOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [{ key: t('AllFleets'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        const response = await fleetApi.getAdminFleetPage({
          page: page,
          size: 100,
          sort: 'fleetName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.fleetName && item.fleetId !== undefined)
          .map((item) => ({
            key: item.fleetName!,
            value: item.fleetId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllFleets'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const dealerOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [{ key: t('AllDealers'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        const response = await dealerApi.getAdminDealerPage({
          page: page,
          size: 100,
          sort: 'dealerName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.dealerName && item.dealerId !== undefined)
          .map((item) => ({
            key: item.dealerName!,
            value: item.dealerId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllDealers'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const serviceCenterOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [{ key: t('AllServiceCenters'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        const response = await serviceCenterApi.getAdminServiceCenterPage({
          page: page,
          size: 100,
          sort: 'serviceCenterName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter(
            (item) =>
              item.serviceCenterName && item.serviceCenterId !== undefined,
          )
          .map((item) => ({
            key: item.serviceCenterName!,
            value: item.serviceCenterId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllServiceCenters'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  return (
    <CustomFrame
      name={t('VehicleRegistration')}
      back={true}
      onBackClick={pageLeave}
      className="[&_article]:mb-5 [&_article]:space-y-3 [&_article]:[&>div]:gap-10 [&_h2]:mb-6 [&_h2]:subtitle3 [&_em]:text-semantic-4"
    >
      <form onSubmit={handleSubmit(handleRegister)}>
        <div className="w-full relative">
          <div className="f-c gap-[10px] absolute top-[-59px] right-0">
            <Button
              type="submit"
              variant={'bt_primary'}
              label={t('Register')}
              disabled={!isValid}
            />
          </div>
        </div>

        {/* 기본 정보 */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('BasicInformation')}</h2>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('VINNumber')} <em>*</em>
              </SearchLabel>
              <div className="f-s gap-3">
                <Input
                  placeholder={t('VINNumber')}
                  widthSize={'md'}
                  showCancel={false}
                  error={errors.vinNumber?.message}
                  {...register('vinNumber')}
                />
                <Button
                  type="button"
                  variant={'bt_tertiary'}
                  label={t('Validate')}
                  onClick={handleInspectVin}
                />
              </div>
            </SearchItemContainer>
          </div>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('Manufacturer')} <em>*</em>
              </SearchLabel>
              <Input
                placeholder={t('Manufacturer')}
                widthSize={'lg'}
                error={errors.manufacturer?.message}
                {...register('manufacturer')}
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <SearchLabel>
                {t('ModelName')} <em>*</em>
              </SearchLabel>
              <Input
                placeholder={t('ModelName')}
                widthSize={'lg'}
                error={errors.modelName?.message}
                {...register('modelName')}
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <SearchLabel>
                {t('TrimName')} <em>*</em>
              </SearchLabel>
              <Input
                widthSize={'lg'}
                error={errors.trimName?.message}
                {...register('trimName')}
              />
            </SearchItemContainer>
          </div>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('ManufactureYear')} <em>*</em>
              </SearchLabel>
              <Controller
                control={control}
                name="yearOfManufacture"
                render={({ field, fieldState }) => (
                  <>
                    <YearSelector
                      value={field.value}
                      onValueChange={(v) =>
                        field.onChange(dayjs(v as Date).format('YYYY'))
                      }
                    />
                    {fieldState.error?.message && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>
                {t('VehicleNumber')} <em>*</em>
              </SearchLabel>
              <Input
                placeholder={t('VehicleNumber')}
                widthSize={'md'}
                error={errors.plateNo?.message}
                {...register('plateNo')}
              />
            </SearchItemContainer>
          </div>
        </article>

        {/* 차량 스펙 */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('VehicleSpecifications')}</h2>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('VehicleType')} <em>*</em>
              </SearchLabel>
              <Controller
                control={control}
                name="vehicleType"
                render={({ field, fieldState }) => (
                  <div className="flex flex-col">
                    <DropDown
                      options={vehicleTypeOptions}
                      placeholder={t('VehicleType')}
                      size="md"
                      selectedKey={field.value.key}
                      onSelPair={(key, value) => field.onChange({ key, value })}
                    />
                    {fieldState.error?.message && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('BodyClass')}</SearchLabel>
              <Controller
                control={control}
                name="bodyClass"
                render={({ field }) => (
                  <DropDown
                    options={bodyClassOptions}
                    placeholder={t('BodyClass')}
                    size="md"
                    selectedKey={field.value.key}
                    onSelPair={(key, value) => field.onChange({ key, value })}
                  />
                )}
              />
            </SearchItemContainer>
          </div>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('Length')} <em>*</em>
              </SearchLabel>
              {distanceUnit === 'km' ? (
                <Input
                  placeholder={t('km')}
                  widthSize="sm"
                  error={errors.lengthKm?.message}
                  {...register('lengthKm')}
                />
              ) : (
                <>
                  <Input
                    placeholder={t('Ft')}
                    widthSize="sm"
                    error={errors.lengthFt?.message}
                    {...register('lengthFt')}
                  />
                  <Input
                    placeholder={t('in')}
                    widthSize="sm"
                    error={errors.lengthIn?.message}
                    {...register('lengthIn')}
                  />
                </>
              )}
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>
                {t('Height')} <em>*</em>
              </SearchLabel>
              {distanceUnit === 'km' ? (
                <Input
                  placeholder={t('km')}
                  widthSize="sm"
                  error={errors.heightKm?.message}
                  {...register('heightKm')}
                />
              ) : (
                <>
                  <Input
                    placeholder={t('Ft')}
                    widthSize="sm"
                    error={errors.heightFt?.message}
                    {...register('heightFt')}
                  />
                  <Input
                    placeholder={t('in')}
                    widthSize="sm"
                    error={errors.heightIn?.message}
                    {...register('heightIn')}
                  />
                </>
              )}
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>
                {t('Width')} <em>*</em>
              </SearchLabel>
              {distanceUnit === 'km' ? (
                <Input
                  placeholder={t('km')}
                  widthSize="sm"
                  error={errors.widthKm?.message}
                  {...register('widthKm')}
                />
              ) : (
                <>
                  <Input
                    placeholder={t('Ft')}
                    widthSize="sm"
                    error={errors.widthFt?.message}
                    {...register('widthFt')}
                  />
                  <Input
                    placeholder={t('in')}
                    widthSize="sm"
                    error={errors.widthIn?.message}
                    {...register('widthIn')}
                  />
                </>
              )}
            </SearchItemContainer>
          </div>
          <div className="pt-[10px]">
            <SearchItemContainer
              style={{ alignItems: 'start' }}
              className="flex-col"
            >
              <SearchLabel>{t('HazardousMaterials')}</SearchLabel>
              <div className="flex flex-wrap gap-8 w-full">
                {hazardousMaterialsList.map((material) => {
                  const selected = watch('hazardousMaterials') || [];
                  return (
                    <Controller
                      key={material}
                      control={control}
                      name="hazardousMaterials"
                      render={({ field }) => (
                        <CheckBox
                          label={t(material)}
                          checked={selected.includes(material)}
                          onCheckedChange={(checked) => {
                            const newValue = checked
                              ? [...selected, material]
                              : selected.filter((m) => m !== material);
                            field.onChange(newValue);
                          }}
                        />
                      )}
                    />
                  );
                })}
              </div>
            </SearchItemContainer>
          </div>
        </article>

        {/* 효율성 */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('Efficiency')}</h2>
          <div>
            <SearchItemContainer>
              <SearchLabel>
                {t('FuelType')} <em>*</em>
              </SearchLabel>
              <Controller
                control={control}
                name="fuelType"
                render={({ field, fieldState }) => (
                  <div className="flex flex-col">
                    <DropDown
                      options={fuelTypeOptions}
                      placeholder={t('FuelType')}
                      size="md"
                      selectedKey={field.value.key}
                      onSelPair={(key, value) => field.onChange({ key, value })}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('FuelEfficiency')}</SearchLabel>
              <Input
                placeholder={t('FuelEfficiencyKmLi')}
                widthSize="md"
                error={errors.fuelEfficiency?.message}
                {...register('fuelEfficiency')}
              />
              <Controller
                control={control}
                name="fuelEfficiencyUnit"
                render={({ field, fieldState }) => (
                  <div className="flex flex-col">
                    <DropDown
                      options={fuelEfficiencyUnitOptions}
                      placeholder="MPG"
                      selectedKey={field.value.key}
                      onSelPair={(key, value) => field.onChange({ key, value })}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <SearchLabel>{t('FuelTankCapacity')}</SearchLabel>
              <Input
                placeholder={t('FuelTankCapacityL')}
                widthSize="md"
                error={errors.fuelTankCapacity?.message}
                {...register('fuelTankCapacity')}
              />
              <Controller
                control={control}
                name="fuelTankCapacityUnit"
                render={({ field, fieldState }) => (
                  <div className="flex flex-col">
                    <DropDown
                      options={fuelTankCapacityUnitOptions}
                      placeholder="Gal"
                      selectedKey={field.value.key}
                      onSelPair={(key, value) => field.onChange({ key, value })}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </SearchItemContainer>
          </div>

          <div>
            <SearchItemContainer>
              <SearchLabel>{t('TireDiameterinch')}</SearchLabel>
              <Input
                placeholder={t('TireDiameterinch')}
                widthSize="md"
                error={errors.tireDiameter?.message}
                {...register('tireDiameter')}
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <SearchLabel>{t('TireWidthmm')}</SearchLabel>
              <Input
                placeholder={t('TireWidthmm')}
                widthSize="md"
                error={errors.tireWidth?.message}
                {...register('tireWidth')}
              />
            </SearchItemContainer>
          </div>
        </article>

        {/* 소모품 항목 및 교체/정비 기준 */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10 [&_span]:w-[140px] [&_p]:input-design">
          <h2 className="f-c-b">
            {t('ConsumableItemsAndReplacementMaintenanceCriteria')}
            <Button
              type="button"
              variant="bt_tertiary_sm"
              label={t('Reset')}
              onClick={() => {
                const unit = watch('consumableUnit');
                replace(
                  unit === 'km'
                    ? defaultConsumableItems.km
                    : defaultConsumableItems.mi,
                );
              }}
            />
          </h2>
          {[0, 1, 2].map((rowIdx) => (
            <div key={rowIdx}>
              {fields
                .filter((item) => {
                  const fuel = watch('fuelType').value;
                  if (fuel === 'electric') {
                    if (item.key === 'Battery') return true;
                    if (consumableHideKeysForElectric.includes(item.key ?? ''))
                      return false;
                  }
                  return true;
                })
                .slice(rowIdx * 4, rowIdx * 4 + 4)
                .map((item, idxInSlice) => {
                  // ✅ 실제 index 계산
                  const actualIndex = rowIdx * 4 + idxInSlice;

                  return (
                    <SearchItemContainer key={item.id ?? item.key}>
                      <SearchLabel>{t(item.key ?? '')}</SearchLabel>
                      <Controller
                        control={control}
                        name={`consumableItems.${actualIndex}.value`}
                        render={({ field }) => (
                          <Input
                            placeholder="0"
                            widthSize="md"
                            error={
                              errors.consumableItems?.[actualIndex]?.value
                                ?.message
                            }
                            suffix={
                              item.key === 'Coolant' || item.key === 'Battery'
                                ? 'years'
                                : distanceUnit
                            }
                            {...field}
                            value={field.value?.toString() ?? ''} // field.value로 상태를 유지
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value) || 0)
                            }
                          />
                        )}
                      />
                    </SearchItemContainer>
                  );
                })}
            </div>
          ))}
        </article>

        {/* Fleet Assignment */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('FleetAssignment')}</h2>
          <SearchItemContainer>
            <SearchLabel>{t('Fleet')}</SearchLabel>
            <Controller
              control={control}
              name="fleet"
              render={({ field }) => (
                <DropDownPaginated
                  loadOptions={fleetOptions}
                  placeholder={t('SelectFleet')}
                  size="lg"
                  selectedKey={field.value?.key || ''}
                  onSelPair={(key, value) => field.onChange({ key, value })}
                />
              )}
            />
          </SearchItemContainer>
        </article>

        {/* Dealer Information */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('DealerInformation')}</h2>
          <SearchItemContainer>
            <SearchLabel>{t('Dealer')}</SearchLabel>
            <Controller
              control={control}
              name="dealer"
              render={({ field }) => (
                <DropDownPaginated
                  loadOptions={dealerOptions}
                  placeholder={t('SelectDealer')}
                  size="lg"
                  selectedKey={field.value?.key || ''}
                  onSelPair={(key, value) => field.onChange({ key, value })}
                />
              )}
            />
          </SearchItemContainer>
        </article>

        {/* Service Center Information */}
        <article className="w-b-b-r-30 [&>div]:f-c [&>div]:gap-10">
          <h2>{t('ServiceCenterInformation')}</h2>
          <SearchItemContainer>
            <SearchLabel>{t('ServiceCenter')}</SearchLabel>
            <Controller
              control={control}
              name="serviceCenter"
              render={({ field }) => (
                <DropDownPaginated
                  loadOptions={serviceCenterOptions}
                  placeholder={t('SelectServiceCenter')}
                  size="lg"
                  selectedKey={field.value?.key || ''}
                  onSelPair={(key, value) => field.onChange({ key, value })}
                />
              )}
            />
          </SearchItemContainer>
        </article>
      </form>
    </CustomFrame>
  );
};

export default VehicleRegistration;
