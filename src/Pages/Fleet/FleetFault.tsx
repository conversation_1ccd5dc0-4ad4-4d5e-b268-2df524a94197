import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import FleetFaultOccurrence from '@/Pages/Fleet/FleetFault/FleetFaultOccurrence.tsx';
import FleetFaultPast from '@/Pages/Fleet/FleetFault/FleetFaultPast.tsx';

const FleetFault = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('CurrentFault');

  return (
    <CustomFrame name={t('FaultManagement')} back={false}>
      <section>
        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={'CurrentFault'}>
              <span>{t('CurrentFault')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'PastFault'}>
              <span>{t('PastFault')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          {/*  */}
          <div className={'tab-wrap'}>
            <FleetFaultOccurrence />
            <FleetFaultPast />
          </div>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default FleetFault;
