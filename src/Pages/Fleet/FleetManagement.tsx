import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom'; // 수정: useSearchParams 사용
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import FleetInfo from '@/Pages/Fleet/FleetManagement/FleetInfo.tsx';
import FleetEquipmentInfo from '@/Pages/Fleet/FleetManagement/FleetEquipmentInfo.tsx';

const FleetManagement = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();

  // 장비 상세 정보 뒤로 가기 탭 활성화
  const fleetInfoTab = searchParams.get('activeTab') || 'FleetInfo';
  const [value, setValue] = useState(fleetInfoTab);

  useEffect(() => {
    setSearchParams({ activeTab: value });
  }, [value, setSearchParams]);

  return (
    <CustomFrame name={t('FleetManagement')} back={false}>
      <section>
        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={'FleetInfo'}>
              <span>{t('FleetInfo')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'EquipmentInfo'}>
              <span>{t('EquipmentInfo')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          <div className={'w-b-b-r-30-2 flex-1'}>
            <FleetInfo />
            <FleetEquipmentInfo />
          </div>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default FleetManagement;
