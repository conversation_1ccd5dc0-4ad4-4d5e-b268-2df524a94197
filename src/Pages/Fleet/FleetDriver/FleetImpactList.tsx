import { ChangeEvent, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import Dropdown from '@/Common/Components/common/DropDown';
import CommonTable from '@/Common/Components/common/CommonTable';
import edit from '@/assets/images/ic/24/edit.svg';

interface CollisionInfo {
  rowNumber?: number;
  siteId: string;
  model: string;
  num: string;
  collision: string;
  accident: string;
  overSpeed: string;
  notsent: string;
  edit: string;
  totalCount?: number;
}

interface DriverApiGetCollisionPageRequest {
  pageNum: number;
  pageSize: number;
  siteId: string;
  smodel?: string;
  model?: string;
}

const FleetCollisionList = () => {
  const { t } = useTranslation();

  const [selectedCheck, setSelectedCheck] = useState<number[]>([]);

  const handleSelectionChange = (selectedRows: CollisionInfo[]) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => row.rowNumber ?? 0));
    }
  };

  const { openDImpactDeletePopup, openDShockManagementPopup } = UseFleetPopup();

  // 입력 필드 값을 저장할 상태
  const [inputValues, setInputValues] = useState({
    siteId: '',
    smodel: '',
    model: '',
  });

  // 핸들러
  const handleInputChange = (field: string, value: string) => {
    console.log(value);
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(25); // Mock total count

  const [requestParams, setRequestParams] =
    useState<DriverApiGetCollisionPageRequest>({
      pageNum: 1,
      pageSize: pageSize,
      siteId: '0',
      smodel: undefined,
      model: undefined,
    });

  // Mock 충돌 데이터
  const mockCollisionData: CollisionInfo[] = useMemo(
    () => [
      {
        rowNumber: 1,
        siteId: 'Fleet A',
        model: '25B-X',
        num: '25B-12421',
        collision: '3.0 / 3.0 / 3.0',
        accident: '4.0 / 4.0 / 4.0',
        overSpeed: '85 km/h',
        notsent: 'No',
        edit: 'edit',
        totalCount: 25,
      },
    ],
    [],
  );

  const fleetCollisionPageList = useMemo(() => {
    return mockCollisionData.map((item: CollisionInfo) => ({
      ...item,
      edit: 'edit',
    }));
  }, [mockCollisionData]);

  const handleSearch = () => {
    setRequestParams((prev) => ({
      ...prev,
      ...inputValues,
      pageNum: 1,
    }));
    console.log('Search params:', { ...requestParams, ...inputValues });
  };

  // 검색 조건 변경 함수
  const updateSearchParams = (
    field: keyof DriverApiGetCollisionPageRequest,
    value: string | number | boolean,
  ) => {
    setRequestParams((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const fleetOptions = [
    { key: 'Fleet1', value: 'fleet1' },
    { key: 'Fleet2', value: 'fleet2' },
    { key: 'Fleet3', value: 'fleet3' },
  ];

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    updateSearchParams('pageNum', newPage);
  };

  const columns = [
    {
      header: t('FleetName'),
      accessorKey: 'siteId',
      cell: () => (
        <Dropdown
          size={'no'}
          onChange={() => undefined}
          options={fleetOptions}
          placeholder={'CARTA FLEET'}
        />
      ),
    },
    {
      header: t('ModelName'),
      accessorKey: 'model',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'num',
    },
    {
      header: () => (
        <div className="flex flex-col">
          {t('ImpactThresholdG')}
          <span className="body4 text-gray-10">
            {t('FrontRearSideVertical')}
          </span>
        </div>
      ),
      accessorKey: 'collision',
    },
    {
      header: () => (
        <div className="flex flex-col">
          {t('AccidentCriteriaG')}{' '}
          <span className="body4 text-gray-10">
            {t('FrontRearSideVertical')}
          </span>
        </div>
      ),
      accessorKey: 'accident',
    },
    {
      header: t('OverspeedThreshold'),
      accessorKey: 'overSpeed',
    },
    {
      header: t('Unsent'),
      accessorKey: 'notsent',
    },
    {
      header: t('Edit'),
      accessorKey: 'edit',
      cell: () => (
        <img
          src={edit}
          alt="edit"
          onClick={openDShockManagementPopup}
          className="cursor-pointer"
        />
      ),
    },
  ];

  return (
    <Tabs.Content value={'FleetImpactList'} className={'w-b-b-r-30'}>
      {/* 필터 */}
      <h2 className="mb-[18px] subtitle3">{t('ImpactInformationReport')}</h2>
      <article className="mb-[18px] f-e-b">
        <div className="f-c gap-4">
          <div className="f-c gap-[10px]">
            <Input placeholder={t('FleetName')} />
            <Input
              placeholder={t('ModelName')}
              name="smodel"
              value={inputValues.smodel}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                const { name, value } = e.target;
                handleInputChange(name, value);
              }}
              reset={() => {
                handleInputChange('smodel', '');
              }}
            />
            <Input
              placeholder={t('VehicleNumber')}
              name="model"
              value={inputValues.model}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                const { name, value } = e.target;
                handleInputChange(name, value);
              }}
              reset={() => {
                handleInputChange('model', '');
              }}
            />
          </div>
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={handleSearch}
          />
        </div>
      </article>

      {/* 테이블 */}
      <div>
        <div className="mb-[10px] f-c-e gap-[10px]">
          <Button
            variant={'bt_primary_sm'}
            label={'Delete'}
            disabled={selectedCheck.length === 0}
            onClick={() => openDImpactDeletePopup()}
          />
          <Button variant={'bt_tertiary_sm'} label={'Download'} />
        </div>
        <CommonTable
          columns={columns}
          data={fleetCollisionPageList || []}
          isPagination={true}
          isCheckbox={true}
          customPageSize={pageSize}
          currentPage={requestParams.pageNum}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          onSelectionChange={handleSelectionChange}
        />
      </div>
    </Tabs.Content>
  );
};

export default FleetCollisionList;
