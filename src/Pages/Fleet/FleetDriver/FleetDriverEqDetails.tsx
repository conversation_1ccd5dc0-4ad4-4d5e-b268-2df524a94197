import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { DemoTest } from '@/types';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SummaryData from '@/Common/Components/etc/SummaryData';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useLocation, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { equipmentMonitoringApi, equipmentApi } from '@/api';

type SummaryResult = {
  label: string;
  value?: string | number | React.ReactNode | null;
}[];

type DriverPageParams = {
  equipmentId: number;
  page: number;
  size: number;
  sort: string;
};

type DriverPage = {
  rows: DriverRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type DriverRow = {
  driverName: string;
  idType: string;
  licStartDate: string;
  licEndDate: string;
  licStartTime: string;
  licEndTime: string;
  equipmentCount: number;
};

const FleetDriverDetails = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const equipmentId: number = location.state?.equipmentId || 0;

  /** useParam */
  const [driverPageParams, setDriverPageParams] = useState<DriverPageParams>({
    equipmentId: equipmentId,
    page: 0,
    size: 10,
    sort: 'driverName,asc',
  });

  /** useQuery */
  const { data: summaryResult } = useQuery<SummaryResult>({
    queryKey: ['/api/equipment/monitoring/detail', equipmentId],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return [];
      } else {
        try {
          const response =
            await equipmentMonitoringApi.getAdminEquipmentForMonitoring({
              equipmentId: equipmentId,
            });

          const result: SummaryResult = [];

          if (response.data) {
            result.push({
              label: t('Model'),
              value: response.data.modelName ?? '-',
            });
            result.push({
              label: t('VehicleNumber'),
              value: response.data.plateNo ?? '-',
            });
            result.push({
              label: t('SerialNo'),
              value: response.data.serialNo ?? '-',
            });
            result.push({
              label: t('Country'),
              value: response.data.country?.countryName ?? '-',
            });
            result.push({
              label: t('DealerName'),
              value: response.data.dealer?.dealerName ?? '-',
            });
            result.push({ label: t('Address'), value: '-' });
          }
          return result;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: [],
    enabled: true,
  });

  const { data: driverPage } = useQuery<DriverPage | null>({
    queryKey: ['/api/equipment/driver/page', driverPageParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          const response =
            await equipmentApi.getAdminDriverPageOfEquipment(driverPageParams);
          if (response.data && response.data.content && response.data.page) {
            const result: DriverPage = {
              rows: [],
              page: {
                pageSize: 0,
                totalCnt: 0,
                pageNum: 0,
              },
            };

            response.data.content.forEach((row, index) => {
              result.rows.push({
                driverName: row.driverName ?? '',
                idType: '',
                licStartDate: '',
                licEndDate: '',
                licStartTime: '',
                licEndTime: '',
                equipmentCount: row.equipmentCount ?? 0,
              });

              if (response.data.page) {
                result.page.pageSize =
                  response.data.page.size ?? driverPageParams.size;
                result.page.totalCnt = response.data.page.totalElements ?? 0;
                result.page.pageNum =
                  response.data.page.number ?? driverPageParams.page;
              }
            });
            return result;
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    //initialData: {},
    enabled: true,
  });

  // Eq Table
  const eqColumns = [
    {
      header: t('DriverName'),
      accessorKey: 'driverName',
    },
    {
      header: t('IDType'),
      accessorKey: 'idType',
    },
    {
      header: t('LicenseStartDate'),
      accessorKey: 'licStartDate',
    },
    {
      header: t('LicenseExpiryDate'),
      accessorKey: 'licEndDate',
    },
    {
      header: t('StartTime'),
      accessorKey: 'licStartTime',
    },
    {
      header: t('EndTime'),
      accessorKey: 'licEndTime',
    },
    {
      header: t('AvailableEquipmentCount'),
      accessorKey: 'equipmentCount',
    },
  ];

  const handleBack = () => {
    navigate('/fleet_driver', {
      state: {
        activeTab: location.state?.activeTab,
        eqParams: location.state?.eqParams,
      },
    });
  };

  return (
    <CustomFrame name={t('DriverDetails')} back={true} onBackClick={handleBack}>
      <section>
        <article className="mb-6 w-b-b-r-30">
          <SummaryData details={summaryResult} fs="lg" />
        </article>

        <article className="w-b-b-r-30">
          <div className="mb-3 f-c gap-3">
            <h2 className="body2">{t('Drivers')}</h2>
            <p className="subtitle4 text-primary-10">{t('Total')} 6</p>
          </div>
          <CommonTable
            columns={eqColumns}
            data={driverPage?.rows || []}
            isCheckbox={false}
            isPagination={true}
            customPageSize={driverPage?.page.pageSize ?? 0}
            totalCount={driverPage?.page.totalCnt ?? 0}
            currentPage={
              driverPage?.page.pageNum ? driverPage.page.pageNum + 1 : 1
            }
            onPageChange={(page: number) => {
              setDriverPageParams((prevState) => ({
                ...prevState,
                page: page - 1,
              }));
            }}
          />
        </article>
      </section>
    </CustomFrame>
  );
};

export default FleetDriverDetails;
