import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useQuery } from '@tanstack/react-query';
import {
  DemoTest,
  DriverStatusType,
  toDriverStatusType,
  EnumItemType,
} from '@/types';
import { driverApi, enumApi } from '@/api';
import { ColumnDef } from '@tanstack/react-table';
import { useForm } from 'react-hook-form';
import DriverStatusBadge from '@/Common/Components/common/DriverStatusBadge';
import { GetAdminDriverPageDriverStatusListEnum } from '@/api/generated';

type DriverInfoParams = {
  driverStatusList: GetAdminDriverPageDriverStatusListEnum[];
  driverName: string;
  page: number;
  size: number;
  sort: string;
};

type DriverInfoPage = {
  rows: DriverInfoRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type DriverInfoRow = {
  driverId: number;
  driverName: string;
  phoneNumber: string;
  email: string;
  license: string;
  class: string;
  status: DriverStatusType;
};

const FleetDriverList = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      modelName: '',
      plateNo: '',
      driverName: '',
    },
  });
  const { openFDDriverRegistrationPopup, openFDDriverDelPopup } =
    UseFleetPopup();

  const routeManualRegister = () => {
    navigate('/fleet_driver/fleetDriverRegistration');
  };

  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // 입력 폼 값
  const [formValues, setFormValues] = useState({
    driverStatus: { key: '', value: '' },
    driverName: '',
  });

  /** Params */
  const [driverInfoPageParams, setDriverInfoPageParams] = useState<
    DriverInfoParams | undefined
  >(
    location.state?.driverParams ?? {
      driverStatusList: [],
      driverName: '',
      page: 0,
      size: 10,
      sort: 'driverName,asc',
    },
  );

  /** useQuery */
  const { data: driverInfoPage } = useQuery<DriverInfoPage | null>({
    queryKey: ['/api/driver/page', driverInfoPageParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          if (driverInfoPageParams) {
            const response =
              await driverApi.getAdminDriverPage(driverInfoPageParams);
            if (response.data && response.data.content && response.data.page) {
              const result: DriverInfoPage = {
                rows: [],
                page: {
                  pageSize: 0,
                  totalCnt: 0,
                  pageNum: 0,
                },
              };

              response.data.content.forEach((row, index) => {
                if (row.driverId !== undefined) {
                  result.rows.push({
                    driverId: row.driverId,
                    driverName: row.driverName ?? '',
                    phoneNumber: row.driverPhone ?? '',
                    email: row.loginId ?? '', //(운전자로그인 아이디, 이메일)
                    license: row.licenseNo ?? '',
                    class: row.licenseClass ?? '',
                    status: toDriverStatusType(row.driverStatus),
                  });
                }
                if (response.data.page) {
                  result.page.pageSize =
                    response.data.page.size ?? driverInfoPageParams.size;
                  result.page.totalCnt = response.data.page.totalElements ?? 0;
                  result.page.pageNum =
                    response.data.page.number ?? driverInfoPageParams.page;
                }
              });
              return result;
            }
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!driverInfoPageParams,
  });

  useEffect(() => {
    resetListCheck();
  }, []);

  // 상세/팝업 라우트
  const routeDriverDetails = (rowData: DriverInfoRow) => {
    navigate('/fleet_driver/fleetDriverDetails', {
      state: {
        driverId: rowData.driverId,
        driverParams: driverInfoPageParams,
      },
    });
  };

  const { data: statusOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'driverStatus', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: t('AllStatus'), value: '' },
          ...Object.values(DriverStatusType).map((status) => ({
            key: t(status),
            value: status === DriverStatusType.OnDuty ? 'ON_DUTY' : 'IDLE',
          })),
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'driverStatus',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  // 운전자 보기 상세보기 이동
  const driverCellRoute = ({
    cell,
    row,
  }: {
    cell: { getValue: () => unknown };
    row: { original: DriverInfoRow };
  }) => (
    <span
      onClick={() => routeDriverDetails(row.original)}
      style={{ cursor: 'pointer' }}
    >
      {cell.getValue() as string}
    </span>
  );

  const refreshList = () => {
    resetListCheck();
    setDriverInfoPageParams((prevState) =>
      prevState ? { ...prevState, page: 0, _refresh: Date.now() } : undefined,
    );
  };

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  const handleSearch = () => {
    let driverStatusList: GetAdminDriverPageDriverStatusListEnum[] = [];
    if (formValues.driverStatus.value != '') {
      if (formValues.driverStatus.value === 'ON_DUTY') {
        driverStatusList.push(GetAdminDriverPageDriverStatusListEnum.OnDuty);
      } else {
        driverStatusList.push(GetAdminDriverPageDriverStatusListEnum.Idle);
      }
    }
    setDriverInfoPageParams({
      driverStatusList: driverStatusList,
      driverName: formValues.driverName,
      page: 0,
      size: 10,
      sort: 'driverName,asc',
    });
  };

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: DriverInfoRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.driverId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  // 테이블 컬럼 정의
  const driverColumns: ColumnDef<DriverInfoRow>[] = [
    {
      header: t('DriverName'),
      accessorKey: 'driverName',
      cell: driverCellRoute,
    },
    {
      header: t('PhoneNumber'),
      accessorKey: 'phoneNumber',
      cell: driverCellRoute,
    },
    {
      header: t('Email'),
      accessorKey: 'email',
      cell: driverCellRoute,
    },
    {
      header: t('DriversLicenseNumber'),
      accessorKey: 'license',
      cell: driverCellRoute,
    },
    {
      header: t('Class'),
      accessorKey: 'class',
      cell: driverCellRoute,
    },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ row }) => <DriverStatusBadge status={row.original.status} />,
    },
  ];

  return (
    <Tabs.Content value={'DriverManagement'} className={'w-b-b-r-30'}>
      {/* 필터 */}
      <form onSubmit={handleSubmit(handleSearch)}>
        <article className="mb-[18px] f-c gap-4">
          <div className="f-c gap-[10px]">
            <DropDown
              options={statusOptions}
              placeholder={t('AllStatus')}
              size="md"
              selectedKey={formValues.driverStatus.key}
              onSelPair={(key, value) =>
                setFormValues({ ...formValues, driverStatus: { key, value } })
              }
            />
            <Input
              placeholder={t('DriverName')}
              value={formValues.driverName}
              {...register('driverName', {
                maxLength: {
                  value: 20,
                  message: 'Maximum 20 characters allowed.',
                },
              })}
              error={errors.driverName?.message}
              onChange={(e) =>
                setFormValues({ ...formValues, driverName: e.target.value })
              }
              reset={() => setFormValues({ ...formValues, driverName: '' })}
            />
          </div>
          <Button type="submit" variant={'bt_primary'} label={'Search'} />
        </article>
      </form>

      {/* 테이블 */}
      <article>
        <div className="mb-[10px] f-c-e gap-2">
          <Button
            variant={'bt_primary_sm'}
            label={'Delete'}
            onClick={() => openFDDriverDelPopup(checkedRows, refreshList)}
            disabled={checkedRows.length === 0}
          />
          <Button
            variant={'bt_primary_sm'}
            label={'Register'}
            onClick={() =>
              openFDDriverRegistrationPopup({
                onManualRegister: routeManualRegister,
              })
            }
          />
          <Button variant={'bt_tertiary_sm'} label={'Download'} />
        </div>
        <CommonTable
          columns={driverColumns}
          data={driverInfoPage?.rows || []}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
          rowSelection={rowSelection}
          onRowSelectionChange={handleRowSelectionChange}
          isPagination={true}
          customPageSize={driverInfoPage?.page.pageSize ?? 0}
          totalCount={driverInfoPage?.page.totalCnt ?? 0}
          currentPage={
            driverInfoPage?.page.pageNum ? driverInfoPage.page.pageNum + 1 : 1
          }
          onPageChange={(page: number) => {
            setDriverInfoPageParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
            resetListCheck();
          }}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetDriverList;
