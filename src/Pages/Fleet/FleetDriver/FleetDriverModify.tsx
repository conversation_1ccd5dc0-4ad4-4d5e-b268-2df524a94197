import { useTranslation } from 'react-i18next';
import { useState, useRef, useCallback, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Tooltip } from 'radix-ui';
import dayjs from 'dayjs';
import { toast } from '@/Common/useToast';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CheckBox from '@/Common/Components/common/CheckBox';
import FileBadge from '@/Common/Components/common/FileBadge';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import tooltip from '@/assets/images/ic/16/tooltip.svg';
import { driverApi, enumApi, equipmentApi } from '@/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import { DemoTest, DropdownOption, EnumItemType } from '@/types';
import {
  AdminDriverCreateReqDTODriverGenderEnum,
  AdminDriverCreateReqDTOLicenseClassEnum,
  AdminDriverCreateReqDTOLicenseIssueStateEnum,
  AdminDriverUpdateReqDTO,
  AdminDriverUpdateReqDTODriverGenderEnum,
  AdminDriverUpdateReqDTOLicenseClassEnum,
  AdminDriverUpdateReqDTOLicenseIssueStateEnum,
} from '@/api/generated';
import DropDownPaginated from '@/Common/Components/common/DropDownPaginated';

type DriverData = {
  equipmentIdList: { key: string; value: string }[];
  driverId: number;
  loginId: string;
  driverName: string;
  driverCountryDialCode: string;
  driverPhone: string;
  licenseNo: string;
  licenseClass: AdminDriverUpdateReqDTOLicenseClassEnum | undefined;
  licenseIssueState: AdminDriverUpdateReqDTOLicenseIssueStateEnum | undefined;
  licenseExpireDt: string | undefined;
  driverGender: AdminDriverUpdateReqDTODriverGenderEnum | undefined;
  driverManagementId: string;
};

const FleetDriverModify = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const driverId: number = location.state?.driverId || 0;

  const [resetDisabled, setResetDisabled] = useState(false);
  const resetTimeout = useRef<NodeJS.Timeout | null>(null);

  const { openFDPageOutPopup } = UseFleetPopup();

  const schema = yup.object().shape({
    driverName: yup
      .string()
      .required(t('DriverNameIsRequired'))
      .max(20, t('DriverNameCannotExceed20Characters'))
      .matches(
        /^[a-zA-Z가-힣\s]+$/,
        t('OnlyKoreanAndEnglishLettersAreAllowed'),
      ),
    phoneCountry: yup
      .object({ key: yup.string(), value: yup.string() })
      .test('phoneCountry', t('CountryIsRequired'), (value) => !!value?.key),
    phoneNumber: yup
      .string()
      .required(t('PhoneNumberIsRequired'))
      .matches(/^\d{1,15}$/, t('PhoneNumberMustBeNumericAndUpTo15Digits')),
    email: yup
      .string()
      .required(t('EmailIsRequired'))
      .max(64, t('EmailCannotExceed64Characters'))
      .email(t('InvalidEmailAddress')),
    issuingState: yup
      .object({ key: yup.string(), value: yup.string() })
      .test(
        'issuingState',
        t('IssuingStateIsRequired'),
        (value) => !!value?.key,
      ),
    licenseNumber: yup.string().required(t('LicenseNumberIsRequired')),
    class: yup
      .object({ key: yup.string(), value: yup.string() })
      .test('class', t('ClassIsRequired'), (value) => !!value?.key),

    expirationDate: yup.string().required(t('ExpirationDateIsRequired')),
    gender: yup.object({ key: yup.string(), value: yup.string() }).nullable(),
    idNumber: yup.string().max(20, t('IDNumberCannotExceed20Characters')),
    vehicle: yup.array().of(
      yup.object({
        key: yup.string(),
        value: yup.string(),
      }),
    ),
  });
  type DriverFormData = yup.InferType<typeof schema>;

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      driverName: '',
      phoneCountry: { key: '', value: '' },
      phoneNumber: '',
      email: '',
      issuingState: { key: '', value: '' },
      licenseNumber: '',
      class: { key: '', value: '' },
      expirationDate: dayjs().format('YYYY-MM-DD'),
      gender: { key: '', value: '' },
      idNumber: '',
      vehicle: [],
    },
  });

  const pageOut = () => {
    openFDPageOutPopup(() => {
      navigate(-1);
    });
  };

  // 비밀번호 리셋 버튼 1분동안 비활성화
  const passwordReset = () => {
    setResetDisabled(true);
    if (resetTimeout.current) {
      clearTimeout(resetTimeout.current);
    }
    resetTimeout.current = setTimeout(() => {
      setResetDisabled(false);
    }, 60000); // 1분 = 60000ms
    // 실제 reset 동작은 필요하다면 여기에 추가
  };

  const { data: countryDialCodeOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'countryDialCode', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: '+1(USA)', value: 'US' },
          { key: '+1(CAN)', value: 'CA' },
          { key: '+82(KOR)', value: 'KR' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'countryDialCode',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const { data: stateOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'usState', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: 'Alabama', value: 'Alabama' },
          { key: 'Alaska', value: 'Alaska' },
          { key: 'Arizona', value: 'Arizona' },
          { key: 'Arkansas', value: 'Arkansas' },
          { key: 'California', value: 'California' },
          { key: 'Colorado', value: 'Colorado' },
          { key: 'Connecticut', value: 'Connecticut' },
          { key: 'Delaware', value: 'Delaware' },
          { key: 'Florida', value: 'Florida' },
          { key: 'Georgia', value: 'Georgia' },
          { key: 'Hawaii', value: 'Hawaii' },
          { key: 'Idaho', value: 'Idaho' },
          { key: 'Illinois', value: 'Illinois' },
          { key: 'Indiana', value: 'Indiana' },
          { key: 'Iowa', value: 'Iowa' },
          { key: 'Kansas', value: 'Kansas' },
          { key: 'Kentucky', value: 'Kentucky' },
          { key: 'Louisiana', value: 'Louisiana' },
          { key: 'Maine', value: 'Maine' },
          { key: 'Maryland', value: 'Maryland' },
          { key: 'Massachusetts', value: 'Massachusetts' },
          { key: 'Michigan', value: 'Michigan' },
          { key: 'Minnesota', value: 'Minnesota' },
          { key: 'Mississippi', value: 'Mississippi' },
          { key: 'Missouri', value: 'Missouri' },
          { key: 'Montana', value: 'Montana' },
          { key: 'Nebraska', value: 'Nebraska' },
          { key: 'Nevada', value: 'Nevada' },
          { key: 'New Hampshire', value: 'New Hampshire' },
          { key: 'New Jersey', value: 'New Jersey' },
          { key: 'New Mexico', value: 'New Mexico' },
          { key: 'New York', value: 'New York' },
          { key: 'North Carolina', value: 'North Carolina' },
          { key: 'North Dakota', value: 'North Dakota' },
          { key: 'Ohio', value: 'Ohio' },
          { key: 'Oklahoma', value: 'Oklahoma' },
          { key: 'Oregon', value: 'Oregon' },
          { key: 'Pennsylvania', value: 'Pennsylvania' },
          { key: 'Rhode Island', value: 'Rhode Island' },
          { key: 'South Carolina', value: 'South Carolina' },
          { key: 'South Dakota', value: 'South Dakota' },
          { key: 'Tennessee', value: 'Tennessee' },
          { key: 'Texas', value: 'Texas' },
          { key: 'Utah', value: 'Utah' },
          { key: 'Vermont', value: 'Vermont' },
          { key: 'Virginia', value: 'Virginia' },
          { key: 'Washington', value: 'Washington' },
          { key: 'West Virginia', value: 'West Virginia' },
          { key: 'Wisconsin', value: 'Wisconsin' },
          { key: 'Wyoming', value: 'Wyoming' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'usState',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const { data: classOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'licenseClass', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: 'Class A', value: 'CLASS A' },
          { key: 'Class B', value: 'CLASS B' },
          { key: 'Class C', value: 'CLASS C' },
          { key: 'Class D', value: 'CLASS D' },
          { key: 'Class M', value: 'CLASS M' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'licenseClass',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const { data: genderOptions } = useQuery<EnumItemType[]>({
    queryKey: ['/api/common/enum', 'driverGender', i18n.language],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return [
          { key: 'Male', value: 'MALE' },
          { key: 'Female', value: 'FEMALE' },
        ];
      } else {
        try {
          const response = await enumApi.getEnumMap(
            {
              enumType: 'driverGender',
            },
            {
              headers: {
                'Accept-Language': i18n.language == 'ko' ? 'ko-KR' : 'en-US',
              },
            },
          );

          if (!response.data) {
            return [];
          } else {
            return (
              response.data
                .filter((data) => data.label && data.value)
                .map((data) => ({
                  key: data.label!,
                  value: data.value!,
                })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  const vehicleOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [] : [];
      }

      // 실제 API 호출
      try {
        //'/api/equipment/page'
        const response = await equipmentApi.getAdminEquipmentPage({
          page: page,
          size: 100,
          sort: 'modelName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.modelName && item.equipmentId !== undefined)
          .map((item) => ({
            key: `${item.modelName}`,
            value: `${item.equipmentId}`,
          }));

        return page === 0 ? options : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        return [];
      }
    },
    [],
  );

  const selectedVehicles = watch('vehicle') ?? [];

  const vehicleSelect = (key: string, value: string) => {
    setValue(
      'vehicle',
      selectedVehicles.some((n) => n.key === key && n.value === value)
        ? selectedVehicles.filter((n) => n.key !== key && n.value !== value)
        : [...selectedVehicles, { key: key, value: value }],
    );
  };

  const vehicleRemove = (
    key: string | undefined,
    value: string | undefined,
  ) => {
    if (!key || !value) return;
    // 선택된 차량에서 해당 번호 제거
    setValue(
      'vehicle',
      selectedVehicles.filter((n) => n.key !== key && n.value !== value),
    );
  };

  const { data: driverData } = useQuery<DriverData | null>({
    queryKey: ['/api/driver/all-detail', driverId],
    queryFn: async () => {
      try {
        const response = await driverApi.getAdminDriverAllDetail({
          driverId: driverId,
        });

        if (response.data) {
          return {
            equipmentIdList:
              response.data.equipments
                ?.filter(
                  (item) => item.modelName && item.equipmentId !== undefined,
                )
                .map((item) => ({
                  key: `${item.modelName}`,
                  value: `${item.equipmentId!}`,
                })) ?? [],
            driverId: response.data.driverId ?? 0,
            loginId: response.data.loginId ?? '',
            driverName: response.data.driverName ?? '',
            driverCountryDialCode: response.data.driverCountryDialCode ?? '',
            driverPhone: response.data.driverPhone ?? '',
            licenseNo: response.data.licenseNo ?? '',
            licenseClass: response.data.licenseClass,
            licenseIssueState: response.data.licenseIssueState,
            licenseExpireDt: response.data.licenseExpireDt,
            driverGender: response.data.driverGender,
            driverManagementId: response.data.driverManagementId ?? '',
          };
        }
        return null;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: !!driverId,
    initialData: null,
  });

  useEffect(() => {
    if (!driverData) return;

    reset({
      driverName: driverData.driverName ?? '',
      phoneCountry: { key: '', value: '' },
      phoneNumber: driverData.driverPhone ?? '',
      email: driverData.loginId ?? '',
      issuingState: stateOptions.find(
        (opt) => opt.value === driverData.licenseIssueState,
      ) ?? { key: '', value: '' },
      licenseNumber: driverData.licenseNo ?? '',
      class: classOptions.find(
        (opt) => opt.value === driverData.licenseClass,
      ) ?? {
        key: '',
        value: '',
      },
      expirationDate:
        driverData.licenseExpireDt ?? dayjs().format('YYYY-MM-DD'),
      gender: genderOptions.find(
        (opt) => opt.value === driverData.driverGender,
      ) ?? {
        key: '',
        value: '',
      },
      idNumber: driverData.driverManagementId ?? '',
      vehicle: (driverData.equipmentIdList || []).map((item) => ({
        key: item.key,
        value: item.value,
      })),
    });
  }, [driverData]);

  const modifyDriverMutation = useMutation({
    //'/api/driver'
    mutationFn: (params: {
      driverId: number;
      adminDriverUpdateReqDTO: AdminDriverUpdateReqDTO;
    }) => {
      return driverApi.updateAdminDriver(params);
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('DriverInformationHasBeenUpdated'),
      });
      navigate(-1);
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('AddFail'),
      });
    },
  });

  const onSubmit = (data: DriverFormData) => {
    const params: AdminDriverUpdateReqDTO = {
      //equipmentIdList: [], // 장비아이디목록
      fleetIdList: [], // 플릿아이디목록
      //loginId: data.email, //운전자로그인 아이디(이메일)
      driverName: data.driverName, //운전자명
      driverCountryDialCode: data.phoneCountry.value ?? '', //운전자전화번호국가코드
      driverPhone: data.phoneNumber, //운전자전화번호
      licenseIssueState: data.issuingState
        .value as AdminDriverCreateReqDTOLicenseIssueStateEnum, //면허발급주
      licenseNo: data.licenseNumber, //면허번호
      licenseClass: data.class.value as AdminDriverCreateReqDTOLicenseClassEnum, //면허분류
      licenseExpireDt: data.expirationDate, //면허만료일
      //driverGender
      driverManagementId: data.idNumber, //운전자관리용아이디
    };

    //운전자성별
    if (data.gender?.value) {
      params.driverGender =
        data.gender.value == 'MALE'
          ? AdminDriverCreateReqDTODriverGenderEnum.Male
          : AdminDriverCreateReqDTODriverGenderEnum.Female;
    }

    if (data.vehicle && data.vehicle.length > 0) {
      params.equipmentIdList = data.vehicle
        .filter((item) => item.value)
        .map((item) => Number(item.value));
    }

    modifyDriverMutation.mutate({
      driverId: driverId,
      adminDriverUpdateReqDTO: params,
    });
  };

  return (
    <CustomFrame
      // key={driverData?.loginId}
      name={t('DriverInformation')}
      back={true}
      onBackClick={pageOut}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="w-full relative">
          <div className="f-c gap-2 absolute top-[-59px] right-0">
            <Button type="submit" variant={'bt_primary'} label={'Register'} />
          </div>
        </div>

        {Object.keys(errors).length > 0 && (
          <article className="mb-[18px] py-[5px] f-c-c bg-semantic-4-1 rounded text-semantic-4">
            {t('PleaseFillInTheRequiredFields')}
            {Object.values(errors)[0]?.message && (
              <p className="mt-1 caption4 text-semantic-4">
                {Object.values(errors)[0].message}
              </p>
            )}
          </article>
        )}

        <section
          className="
        space-y-5
          [&>article]:w-b-b-r-30
          [&>article]:space-y-3
          [&>article]:grid-cols-3
          [&>article>div]:grid
          [&>article:nth-child(3)>div]:block
          [&>article>div]:grid-cols-3
          [&>article>div]:gap-y-3
          [&>article>div]:gap-x-[50px]
          [&>article>div>div]:f-c
          [&>article>div>div]:gap-5
          [&>article>div>div]:col-span-1
          [&_h2]:mb-6
          [&_h2]:subtitle3
          [&_em]:text-semantic-4
          [&_h3]:w-[150px]
          [&_h3]:f-c
          [&_h3]:gap-[10px]
          [&_h3]:flex-shrink-0
          [&_h3]:body1
        "
        >
          {/* Personal Information */}
          <article>
            <h2>
              {t('PersonalInformation')} <em>*</em>
            </h2>

            <div>
              <div>
                <h3>{t('DriverName')}</h3>
                <Input
                  value={driverData?.driverName || ''}
                  placeholder={t('DriverName')}
                  {...register('driverName')}
                  error={errors.driverName?.message}
                />
              </div>
              <div>
                <h3>{t('PhoneNumber')}</h3>
                <div className="f-s gap-[10px]">
                  <Controller
                    control={control}
                    name="phoneCountry"
                    render={({ field, fieldState }) => (
                      <div className="flex flex-col w-[285px]">
                        <DropDown
                          options={countryDialCodeOptions}
                          placeholder={t('Country')}
                          size="full"
                          selectedKey={
                            field.value?.key == ''
                              ? countryDialCodeOptions.find(
                                  (opt) =>
                                    opt.value ===
                                    driverData?.driverCountryDialCode,
                                )?.key
                              : field.value?.key
                          }
                          onSelPair={(key, value) =>
                            field.onChange({ key, value })
                          }
                        />
                        {fieldState.error?.message && (
                          <p className="mt-1 caption4 text-semantic-4">
                            {fieldState.error.message}
                          </p>
                        )}
                      </div>
                    )}
                  />
                  <Input
                    value={driverData?.driverPhone || ''}
                    placeholder={t('NumberNoDashes')}
                    {...register('phoneNumber')}
                    error={errors.phoneNumber?.message}
                  />
                </div>
              </div>
            </div>

            <div>
              <div>
                <h3>{t('Email')}</h3>
                <Input
                  value={driverData?.loginId || ''}
                  placeholder={t('Email')}
                  {...register('email')}
                  error={errors.email?.message}
                  disabled
                />
              </div>
              <div>
                <h3>
                  {t('Password')}
                  <Tooltip.Provider>
                    <Tooltip.Root>
                      <Tooltip.Trigger asChild>
                        <img
                          src={tooltip}
                          alt="tooltip"
                          className="cursor-pointer"
                        />
                      </Tooltip.Trigger>
                      <Tooltip.Portal>
                        <Tooltip.Content
                          className="TooltipContent"
                          sideOffset={5}
                          side="bottom"
                        >
                          <p className="py-[6px] px-2 bg-primary-4 rounded caption5 text-white text-center whitespace-pre-wrap">
                            {t(
                              'AResetLinkWillBeSentToTheDriversEmailEmailRequestsAreLimitedToOncePerMinute',
                            )}
                          </p>
                        </Tooltip.Content>
                      </Tooltip.Portal>
                    </Tooltip.Root>
                  </Tooltip.Provider>
                </h3>
                <Button
                  variant={'bt_tertiary'}
                  label={t('Reset')}
                  disabled={resetDisabled}
                  onClick={passwordReset}
                />
              </div>
            </div>
          </article>

          {/* License Information */}
          <article>
            <h2>
              {t('LicenseInformation')} <em>*</em>
            </h2>

            <div>
              <div>
                <h3>{t('IssuingState')}</h3>
                <Controller
                  control={control}
                  name="issuingState"
                  render={({ field, fieldState }) => (
                    <div className="w-full flex flex-col">
                      <DropDown
                        options={stateOptions}
                        placeholder={t('Select')}
                        size="full"
                        selectedKey={
                          field.value.key == ''
                            ? stateOptions.find(
                                (opt) =>
                                  opt.value === driverData?.licenseIssueState,
                              )?.key
                            : field.value.key
                        }
                        onSelPair={(key, value) =>
                          field.onChange({ key, value })
                        }
                      />
                      {fieldState.error?.message && (
                        <p className="mt-1 caption4 text-semantic-4">
                          {fieldState.error.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>
              <div>
                <h3>{t('LicenseNumber')}</h3>
                <Input
                  value={driverData?.licenseNo || ''}
                  placeholder={t('LicenseNumber')}
                  {...register('licenseNumber')}
                  error={errors.licenseNumber?.message}
                />
              </div>
              <div>
                <h3 style={{ width: '70px' }}>{t('Class')}</h3>
                <Controller
                  name="class"
                  control={control}
                  render={({ field, fieldState }) => (
                    <div className="flex flex-col">
                      <DropDown
                        options={classOptions}
                        placeholder={t('Class')}
                        size="md"
                        selectedKey={
                          field.value.key == ''
                            ? classOptions.find(
                                (opt) => opt.value === driverData?.licenseClass,
                              )?.key
                            : field.value.key
                        }
                        onSelPair={(key, value) =>
                          field.onChange({ key, value })
                        }
                      />
                      {fieldState.error?.message && (
                        <p className="mt-1 caption4 text-semantic-4">
                          {fieldState.error.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>
              <div>
                <h3>{t('ExpirationDate')}</h3>
                <Controller
                  control={control}
                  name="expirationDate"
                  render={({ field, fieldState }) => (
                    <>
                      <DaySelector
                        size="lg"
                        initValue={driverData?.licenseExpireDt}
                        onChange={(date) =>
                          field.onChange(dayjs(date).format('YYYY-MM-DD'))
                        }
                      />
                      {fieldState.error?.message && (
                        <p className="mt-1 caption4 text-semantic-4">
                          {fieldState.error.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>
          </article>

          {/* Basic Information(Optional) */}
          <article>
            <h2>{t('BasicInformationOptional')}</h2>

            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gap: '50px',
              }}
            >
              <div className="col-span-1">
                <h3>{t('Gender')}</h3>
                <Controller
                  control={control}
                  name="gender"
                  render={({ field, fieldState }) => (
                    <div className="w-full flex flex-col">
                      <DropDown
                        options={genderOptions}
                        placeholder={t('Select')}
                        size="full"
                        selectedKey={
                          field.value?.key == ''
                            ? genderOptions.find(
                                (opt) => opt.value === driverData?.driverGender,
                              )?.key
                            : field.value?.key
                        }
                        onSelPair={(key, value) =>
                          field.onChange({ key, value })
                        }
                      />
                      {fieldState.error?.message && (
                        <p className="mt-1 caption4 text-semantic-4">
                          {fieldState.error.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>
              <div className="col-span-1">
                <h3>
                  {t('IDNumber')}
                  <Tooltip.Provider>
                    <Tooltip.Root>
                      <Tooltip.Trigger asChild>
                        <img
                          src={tooltip}
                          alt="tooltip"
                          className="cursor-pointer"
                        />
                      </Tooltip.Trigger>
                      <Tooltip.Portal>
                        <Tooltip.Content
                          className="TooltipContent"
                          sideOffset={5}
                          side="bottom"
                        >
                          <p className="py-[6px] px-2 bg-primary-4 rounded caption5 text-white">
                            {t(
                              'IfYouHaveAnEmployeeIDOrInternalManagementIDPleaseEnterIt',
                            )}
                          </p>
                        </Tooltip.Content>
                      </Tooltip.Portal>
                    </Tooltip.Root>
                  </Tooltip.Provider>
                </h3>
                <Input
                  value={driverData?.driverManagementId || ''}
                  placeholder={t('IDNumber')}
                  error={errors.idNumber?.message}
                  {...register('idNumber')}
                />
              </div>
            </div>

            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gap: '50px',
              }}
            >
              <div>
                <h3>{t('Vehicle')}</h3>
                <DropDownPaginated
                  loadOptions={vehicleOptions}
                  placeholder="Vehicle Select"
                  size="full"
                  //selectedKey={selectedVehicles[0]}
                  renderOptions={({ options, onSelect }) => (
                    <div className="min-w-full max-h-[400px] w-max p-[5px] w-b-b-r overflow-y-auto shadow-custom overflow-hidden">
                      {options.map((option) => {
                        return (
                          <label
                            htmlFor={`vehicle-${option.value}`}
                            key={option.value}
                            className="w-full py-[10px] px-3 f-c gap-[10px] bg-white rounded-md hover:bg-primary-0 transition-colors duration-150 cursor-pointer"
                          >
                            <CheckBox
                              id={`vehicle-${option.value}`}
                              checked={selectedVehicles.some(
                                (n) =>
                                  n.key === option.key &&
                                  n.value === option.value,
                              )}
                              onCheckedChange={(checked) => {
                                vehicleSelect(option.key, option.value);
                              }}
                            />
                            <div className="f-c body4">
                              <span>{option.key}</span>
                              <div className="divider-v h-[14px] mx-2" />
                              <span>{option.value}</span>
                            </div>
                          </label>
                        );
                      })}
                    </div>
                  )}
                />
              </div>
            </div>

            <div>
              <div>
                <h3>{t('Selected')}</h3>
                <div className="f-c gap-2 flex-wrap max-h-[120px] overflow-y-auto">
                  {selectedVehicles.length === 0 ? (
                    <span>-</span>
                  ) : (
                    selectedVehicles.map((vehicle) => {
                      return (
                        <FileBadge
                          key={vehicle.value}
                          message={
                            <span className="f-c body4">
                              <span>{vehicle.key}</span>
                              <span className="divider-v h-[14px] mx-2" />
                              <span>{vehicle.value}</span>
                            </span>
                          }
                          onClick={() =>
                            vehicleRemove(vehicle.key, vehicle.value)
                          }
                        />
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </article>
        </section>
      </form>
    </CustomFrame>
  );
};
export default FleetDriverModify;
