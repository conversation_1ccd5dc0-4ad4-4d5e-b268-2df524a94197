import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import Dropdown from '@/Common/Components/common/DropDown';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import DropDownMulti from '@/Common/Components/common/DropDownMulti';
import Input from '@/Common/Components/common/Input';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import Checkbox from '@/Common/Components/common/CheckBox';
import { Button } from '@/Common/Components/common/Button';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useEffect, useState } from 'react';
import { calendarTextColor } from '@/Common/function/utils.ts';
import TimeSelector from '@/Common/Components/datePicker/TimeSelector';
import dayjs from 'dayjs';
import { AlertPopupProps } from '@/types';
import { CalendarType } from '@/types/CalendarType';

const calendarSchema = z.object({
  calendarType: z.string(),
  publicStatus: z.string(),
  countryList: z.any(),
  colorType: z.string(),
  title: z.string(),
  context: z.string(),
  repeatType: z.string(),
  startDate: z.string(),
  startTime: z.string(),
  endDate: z.string(),
  endTime: z.string(),
});

type CalendarDataValues = z.infer<typeof calendarSchema>;

const ScheduleAddPopup = ({
  isOpen,
  onClose,
  calendarInfo,
}: AlertPopupProps & { calendarInfo?: CalendarType.CalendarInfo }) => {
  const { t } = useTranslation();

  const [selectedCalendarType, setSelectedCalendarType] = useState<string>(
    calendarInfo?.calendarType || 'DRIVER',
  );
  const [selectedPublicStatus, setSelectedPublicStatus] = useState<string>(
    calendarInfo?.publicStatus || 'ALL',
  );

  const calendarTypeOptions: RadioOption[] = [
    { value: 'DRIVER', label: 'Driver' },
    { value: 'EQUIPMENT', label: 'Equipment' },
    { value: 'EVENT', label: 'Event' },
  ];

  const publicStatusOptions: RadioOption[] = [
    { value: 'ALL', label: 'AllP' },
    { value: 'PARTIAL', label: 'RestrictedP' },
  ];

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    getValues,
    watch,
  } = useForm<CalendarDataValues>({
    resolver: zodResolver(calendarSchema),
    mode: 'onChange',
    defaultValues: {
      calendarType: calendarInfo?.calendarType || 'DRIVER',
      publicStatus: calendarInfo?.publicStatus || 'ALL',
      countryList: calendarInfo?.countryList || undefined,
      colorType: calendarInfo?.colorType || 'BLUE',
      title: calendarInfo?.title || '',
      context: calendarInfo?.context || '',
      repeatType: calendarInfo?.repeatType || 'NONE',
      startDate:
        calendarInfo?.startDate || dayjs(new Date()).format('YYYY-MM-DD'),
      startTime: calendarInfo?.startTime?.slice(0, 5) || '10:00',
      endDate: calendarInfo?.endDate || dayjs(new Date()).format('YYYY-MM-DD'),
      endTime: calendarInfo?.endTime?.slice(0, 5) || '10:00',
    },
  });

  const colorOptions = [
    {
      key: t('Red'),
      value: 'RED',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('RED'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      key: t('Orange'),
      value: 'ORANGE',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('ORANGE'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      key: t('Green'),
      value: 'GREEN',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('GREEN'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      key: t('Blue'),
      value: 'BLUE',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('BLUE'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      key: t('Purple'),
      value: 'PURPLE',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('PURPLE'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      key: t('Gray'),
      value: 'GRAY',
      icon: (
        <div
          style={{
            backgroundColor: calendarTextColor('GRAY'),
            width: 16,
            height: 16,
            borderRadius: 4,
          }}
        />
      ),
    },
  ];

  // 더미 countrySelectData
  const countrySelectData: CalendarType.SelectItem[] = [];

  const repeatTypeOption = [
    { key: t('N'), value: 'NONE' },
    { key: t('DAILY'), value: 'DAILY' },
    { key: t('WEEKLY'), value: 'WEEKLY' },
    { key: t('MONTHLY'), value: 'MONTHLY' },
    { key: t('YEARLY'), value: 'YEARLY' },
  ];

  const [isAllDay, setIsAllDay] = useState<boolean>(false);

  const startTime: string = watch('startTime');
  const endTime: string = watch('endTime');

  useEffect(() => {
    if (
      getValues('startTime') === '00:00' &&
      getValues('endTime') === '23:59'
    ) {
      setIsAllDay(true);
    } else setIsAllDay(false);
  }, [startTime, endTime]);

  const changedColorTypeText = (colorType: string) => {
    switch (colorType) {
      case 'RED':
        return t('Red');
      case 'PURPLE':
        return t('Purple');
      case 'BLUE':
        return t('Blue');
      case 'GREEN':
        return t('Green');
      case 'GRAY':
        return t('Gray');
      default:
        return t('Orange');
    }
  };

  const changedRepeatTypeText = (colorType: string) => {
    switch (colorType) {
      case 'N':
        return t('N');
      case 'DAILY':
        return t('DAILY');
      case 'WEEKLY':
        return t('WEEKLY');
      case 'MONTHLY':
        return t('MONTHLY');
      default:
        return t('YEARLY');
    }
  };

  // 폼 제출 시 API 호출 없이 닫기만 처리
  const onSubmit = () => {
    onClose();
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>
              {calendarInfo?.calendarId ? t('EditSchedule') : t('AddSchedule')}
            </div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <form onSubmit={handleSubmit(onSubmit)}>
          {Object.keys(errors).length > 0 && (
            <div>
              <pre>{JSON.stringify(errors, null, 2)}</pre>
            </div>
          )}
          <div>
            <div>
              <SearchItemContainer>
                <SearchLabel>{t('TypeC')}</SearchLabel>
                <Radio
                  options={calendarTypeOptions}
                  value={selectedCalendarType}
                  onValueChange={setSelectedCalendarType}
                />
              </SearchItemContainer>
              <SearchItemContainer>
                <Dropdown
                  onChange={(value) => {
                    setValue('colorType', value as string);
                  }}
                  options={colorOptions}
                  placeholder={changedColorTypeText(getValues('colorType'))}
                />
              </SearchItemContainer>
            </div>
            <hr />
            <div>
              <SearchItemContainer>
                <SearchLabel>{t('ViewP')}</SearchLabel>
                <Radio
                  options={publicStatusOptions}
                  value={selectedPublicStatus}
                  onValueChange={setSelectedPublicStatus}
                />
              </SearchItemContainer>
              <SearchItemContainer>
                <SearchItemContainer>
                  <SearchLabel>{t('SelectCountry')}</SearchLabel>
                  <DropDownMulti
                    placeholder={t('CountryMultipleSelect')}
                    selectedKeys={getValues('countryList')}
                    onChange={(values) => {
                      setValue('countryList', values);
                    }}
                    options={countrySelectData || []}
                  />
                </SearchItemContainer>
              </SearchItemContainer>
            </div>
            <div>
              <SearchItemContainer>
                <SearchLabel>{t('Title')}</SearchLabel>
                <Input {...register('title')} placeholder={t('TitleC')} />
              </SearchItemContainer>
              <SearchItemContainer>
                <SearchLabel>{t('Content')}</SearchLabel>
                <textarea
                  placeholder={t('ContentC')}
                  {...register('context')}
                ></textarea>
              </SearchItemContainer>
            </div>
            <SearchItemContainer>
              <SearchLabel>{t('RepeatR')}</SearchLabel>
              <Dropdown
                onChange={(value) => {
                  setValue('repeatType', value as string);
                }}
                options={repeatTypeOption}
                selectedKey={getValues('repeatType')}
                placeholder={changedRepeatTypeText(getValues('repeatType'))}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Start')}</SearchLabel>
              <DaySelector
                onChange={(day) => {
                  setValue('startDate', day);
                }}
                initValue={getValues('startDate')}
              />
              <TimeSelector
                onChange={(value: string | null) => {
                  if (value) setValue('startTime', value);
                }}
                initValue={getValues('startTime')}
              />
            </SearchItemContainer>

            <div>
              <SearchItemContainer>
                <SearchLabel>{t('End')}</SearchLabel>
                <DaySelector
                  onChange={(day) => {
                    setValue('endDate', day);
                  }}
                  initValue={getValues('endDate')}
                />
                <TimeSelector
                  onChange={(value) => {
                    if (value) setValue('endTime', value);
                  }}
                  initValue={getValues('endTime')}
                />
              </SearchItemContainer>
              <SearchItemContainer>
                <div></div>
                <Checkbox
                  label={t('AllDay')}
                  checked={isAllDay}
                  onCheckedChange={(value: boolean) => {
                    setIsAllDay(value);
                    if (value) {
                      setValue('startTime', '00:00');
                      setValue('endTime', '23:59');
                    }
                  }}
                />
              </SearchItemContainer>
            </div>
            <SearchItemContainer>
              <Button
                variant={'bt_primary'}
                label={'Close'}
                onClick={onClose}
              />
              <Button
                variant={'bt_primary'}
                label={calendarInfo?.calendarId ? 'Edit' : 'Add'}
                type="submit"
              />
            </SearchItemContainer>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default ScheduleAddPopup;
