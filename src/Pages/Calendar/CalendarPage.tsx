import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import right from '@/assets/images/arrow/arrow_right.png';
import left from '@/assets/images/arrow/arrow_left.png';
import Dropdown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import { CalendarViewProps, CustomCalendar } from '@/Common/Calendar.tsx';
import dayjs from 'dayjs';
import Calendar from 'react-calendar';
import Checkbox from '@/Common/Components/common/CheckBox';
import { useEffect, useRef, useState, useCallback } from 'react';
import UseSchedulePopup from '@/Pages/Calendar/components/useSchedulePopup.tsx';
import FullCalendar from '@fullcalendar/react';
import { useQuery } from '@tanstack/react-query';
import { EventSourceInput } from '@fullcalendar/core';
import { calendarBgColor, calendarTextColor } from '@/Common/function/utils.ts';
import close_popup from '@/assets/images/etc/close_popup.png';
import useSchedulePopup from '@/Pages/Calendar/components/useSchedulePopup.tsx';
import { Value } from 'react-calendar/src/shared/types.ts';
import { CalendarType } from '@/types/CalendarType';

export type CalendarTypeGroup = Record<string, CalendarType.DayCalendarData[]>;

const CalendarPage = () => {
  const { t, i18n } = useTranslation();
  const [value, setValue] = useState(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  );
  const [view, setView] = useState<CalendarViewProps>(
    CalendarViewProps.DayGridMonth,
  );

  const onNavigate = (value: Date, type: string) => {
    setDayEvents({});
    if (type === '+') {
      if (view === CalendarViewProps.DayGridMonth)
        setValue(
          new Date(value.getFullYear(), value.getMonth() + 1, value.getDate()),
        );
      else if (view === CalendarViewProps.TimeGridWeek)
        setValue(
          new Date(value.getFullYear(), value.getMonth(), value.getDate() + 7),
        );
      else if (view === CalendarViewProps.TimeGridDay)
        setValue(
          new Date(value.getFullYear(), value.getMonth(), value.getDate() + 1),
        );
      else
        setValue(
          new Date(value.getFullYear() + 1, value.getMonth(), value.getDate()),
        );
    } else {
      if (view === CalendarViewProps.DayGridMonth)
        setValue(
          new Date(value.getFullYear(), value.getMonth() - 1, value.getDate()),
        );
      else if (view === CalendarViewProps.TimeGridWeek)
        setValue(
          new Date(value.getFullYear(), value.getMonth(), value.getDate() - 7),
        );
      else if (view === CalendarViewProps.TimeGridDay)
        setValue(
          new Date(value.getFullYear(), value.getMonth(), value.getDate() - 1),
        );
      else
        setValue(
          new Date(value.getFullYear() - 1, value.getMonth(), value.getDate()),
        );
    }
  };

  const { openScheduleAddPopup } = UseSchedulePopup();
  const languageString = i18n.language === 'ko' ? 'ko' : 'en';
  const formatYear = i18n.language === 'ko' ? 'YYYY년' : 'YYYY';
  const formatMonth = i18n.language === 'ko' ? 'YYYY년 MM월' : 'MMM YYYY';
  const formatWeek = i18n.language === 'ko' ? 'YYYY년 MM월' : 'YYYY-MM';
  const formatDay = i18n.language === 'ko' ? 'YYYY년 MM월 DD일' : 'YYYY-MM-DD';

  const format =
    view === CalendarViewProps.DayGridMonth
      ? formatMonth
      : view === CalendarViewProps.TimeGridWeek
        ? formatWeek
        : view === CalendarViewProps.TimeGridDay
          ? formatDay
          : formatYear;

  const ref = useRef<FullCalendar>(null);

  const [loading, setLoading] = useState(false);
  const { data: getCalendarData, refetch: refreshCalendarData } = useQuery({
    queryKey: ['get-calendar-list'],
    queryFn: async () => {
      setLoading(true);
      try {
        return [];
      } finally {
        setLoading(false);
      }
    },
    enabled: true,
  });

  const [events, setEvents] = useState<EventSourceInput>();

  useEffect(() => {
    if (!loading && getCalendarData) {
      const newEvents: EventSourceInput = [];

      setEvents((prevEvents: EventSourceInput | undefined) =>
        JSON.stringify(prevEvents) !== JSON.stringify(newEvents)
          ? newEvents
          : prevEvents,
      );
    }
  }, [getCalendarData, loading]);

  const [checkInfo, setCheckInfo] = useState<{
    diver: boolean;
    eq: boolean;
    event: boolean;
  }>({
    diver: true,
    eq: true,
    event: true,
  });

  const changedCheckInfo = (type: string, isCheck: boolean) => {
    setCheckInfo({
      ...checkInfo,
      [type]: isCheck,
    });
  };

  const [dayParams, setDayParams] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: '',
    endDate: '',
  });

  const { data: calendarDayList, refetch: refreshDayData } = useQuery({
    queryKey: ['get-calendar-day-list', dayParams],
    queryFn: async () => {
      return [];
    },
    enabled: dayParams.startDate !== '',
  });

  const [dayEvents, setDayEvents] = useState<CalendarTypeGroup>({});

  useEffect(() => {
    if (calendarDayList) {
      setDayEvents({});
    } else {
      setDayEvents({});
    }
  }, [calendarDayList]);

  const getCalendarType = (type: string) => {
    if (dayEvents) {
      const findData = dayEvents[type] || [];
      return findData;
    } else return [];
  };

  const SummaryCalendarItem = ({ type }: { type: string }) => {
    const items = getCalendarType(type);
    return items.length > 0 ? (
      <div>
        <span>
          {type === 'DRIVER'
            ? t('DriverInfo')
            : type === 'EQUIPMENT'
              ? t('EquipmentInfo')
              : t('Event')}
        </span>
        {items.map((item, index) => (
          <EventCard key={index} {...item} />
        ))}
        <hr />
      </div>
    ) : null;
  };

  const { openScheduleInfoPopup } = useSchedulePopup();

  const EventCard = (item: CalendarType.DayCalendarData) => (
    <div
      style={{
        borderColor: calendarTextColor(item.colorType),
        backgroundColor: calendarBgColor(item.colorType),
        paddingLeft: 16,
        paddingRight: 16,
        paddingTop: 6,
        paddingBottom: 6,
        borderRadius: 8,
        borderLeft: '4px solid',
        width: 250,
        marginLeft: 8,
        marginRight: 8,
        cursor: 'pointer',
      }}
      onClick={() => {
        if (item.calendarId)
          openScheduleInfoPopup(item.calendarId.toString(), () => {
            console.log('onConfirm 호출됨');
            refreshCalendarData().then((status) => console.log(status));
            if (Object.keys(dayEvents).length)
              refreshDayData().then((status) => console.log(status));
          });
      }}
    >
      <div>
        <p style={{ fontWeight: 'bold', fontSize: 14 }}>{item.time}</p>
        <p style={{ fontSize: 14 }}>{item.title}</p>
      </div>
    </div>
  );

  const [dayType, setDayType] = useState<string>(t('Monthly'));

  return (
    <CustomFrame name={t('Calendar')}>
      <hr />
      <div>
        <div>
          <SearchItemContainer>
            <SearchItemContainer>
              <img
                src={left}
                alt={'left'}
                onClick={() => onNavigate(value, '-')}
              />
              <div>{dayjs(value).format(format)}</div>
              <img
                src={right}
                alt={'right'}
                onClick={() => onNavigate(value, '+')}
              />
            </SearchItemContainer>
            <Dropdown
              options={[
                {
                  key: t('YearAfter'),
                  value: CalendarViewProps.MultiMonthYear,
                },
                { key: t('Monthly'), value: CalendarViewProps.DayGridMonth },
                { key: t('Weekly'), value: CalendarViewProps.TimeGridWeek },
                { key: t('Daily'), value: CalendarViewProps.TimeGridDay },
              ]}
              placeholder={dayType}
              onChange={(e) => {
                console.log(e);
                const e2 = e as CalendarViewProps;
                if (ref.current) {
                  setDayEvents({});
                  setView(e2);
                  ref.current.getApi().changeView(e2, value);
                }
              }}
            />
          </SearchItemContainer>
          <div>
            <CustomCalendar
              ref={ref}
              view={view}
              date={value}
              events={events || []}
              clickMonthDay={useCallback(
                (day: string) => {
                  console.log(day);
                  setDayParams((prev) => {
                    if (prev.startDate === day && prev.endDate === day) {
                      return prev; // 상태가 동일하면 업데이트하지 않음
                    }
                    return {
                      startDate: day,
                      endDate: day,
                    };
                  });
                },
                [setDayParams], // 의존성 배열
              )}
              onRefresh={() => {
                refreshCalendarData().then((status) => console.log(status));
                if (Object.keys(dayEvents).length)
                  refreshDayData().then((status) => console.log(status));
              }}
            />
          </div>
        </div>
        <div>
          <Button variant={'bt_primary'} label={t('AddC')} />
          {Object.keys(dayEvents).length === 0 ? (
            <div>
              <Calendar
                locale={languageString}
                view="month"
                formatDay={(locale, date) => dayjs(date).format('DD')}
                calendarType="gregory"
                formatShortWeekday={(locale, date) => {
                  const dayIndex = dayjs(date).day();
                  return i18n.language === 'ko'
                    ? '일월화수목금토'[dayIndex]
                    : 'SMTWTFS'[dayIndex];
                }}
                onChange={(value: Value) => {
                  setView(CalendarViewProps.TimeGridDay);
                  setDayType(t('Daily'));
                  if (value) setValue(value as Date);
                }}
              />
              <div />
              <div>
                <Checkbox
                  label={t('DriverInfo')}
                  checked={checkInfo.diver}
                  onCheckedChange={(value: boolean) => {
                    changedCheckInfo('diver', value);
                  }}
                />
                <Checkbox
                  label={t('EquipmentInfo')}
                  checked={checkInfo.eq}
                  onCheckedChange={(value: boolean) => {
                    changedCheckInfo('eq', value);
                  }}
                />
                <Checkbox
                  label={t('Event')}
                  checked={checkInfo.event}
                  onCheckedChange={(value: boolean) => {
                    changedCheckInfo('event', value);
                  }}
                />
              </div>
            </div>
          ) : (
            <div>
              <div>
                <span>{dayParams.startDate}</span>
                <img src={close_popup} onClick={() => setDayEvents({})} />
              </div>
              <hr />
              <div>
                {checkInfo.diver ? <SummaryCalendarItem type="DRIVER" /> : null}
                {checkInfo.eq ? <SummaryCalendarItem type="EQUIPMENT" /> : null}
                {checkInfo.event ? <SummaryCalendarItem type="EVENT" /> : null}
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomFrame>
  );
};

export default CalendarPage;
