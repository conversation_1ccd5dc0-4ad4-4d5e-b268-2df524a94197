import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { formatDate } from '@/Common/function/date.ts';
import { DemoTest, DispatchStatusType } from '@/types';
import useDispatchPopup from './components/popup/useDIspatchPopup';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import CommonTable from '@/Common/Components/common/CommonTable';

type DispatchHistoryParams = {
  startDate: string;
  endDate: string;
  plateNo: string;
  driverName: string;
};

type DispatchHistoryPage = {
  rows: DispatchHistoryRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type DispatchHistoryRow = {
  title: string;
  date: string;
  status: DispatchStatusType;
  driver: string;
  vehicle: string;
  total: number;
  deliveries: number;
};

const DispatchHistory = () => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      plateNo: '',
      driverName: '',
    },
  });

  // 입력 폼 값
  const [formValues, setFormValues] = useState({
    startDate: formatDate(new Date()),
    endDate: formatDate(new Date()),
    plateNo: '',
    driverName: '',
  });

  const { openDispatchDetails } = useDispatchPopup();

  const columns: ColumnDef<DispatchHistoryRow>[] = [
    {
      header: t('Title'),
      accessorKey: 'title',
    },
    {
      header: t('Date'),
      accessorKey: 'date',
    },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ row }: { row: Row<DispatchHistoryRow> }) => {
        const value = row.original.status;
        let color = '';
        switch (value) {
          case DispatchStatusType.Completed:
            color = 'text-semantic-1'; // 초록
            break;
          case DispatchStatusType.Pending:
            color = 'text-semantic-4'; // 빨강
            break;
          case DispatchStatusType.InProgress:
            color = 'text-semantic-2'; // 파랑
            break;
          default:
            color = 'text-gray-10';
        }
        return <span className={`${color} body2`}>{value}</span>;
      },
    },
    {
      header: t('DriverName'),
      accessorKey: 'driver',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'vehicle',
    },
    {
      header: t('TotalStops'),
      accessorKey: 'total',
    },
    {
      header: t('CompletedDeliveries'),
      accessorKey: 'deliveries',
    },
  ];

  /** Params */
  const [dispatchHistoryParams, setDispatchHistoryParams] = useState<
    DispatchHistoryParams | undefined
  >();

  /** useQuery */
  const { data: dispatchHistoryPage } = useQuery<DispatchHistoryPage | null>({
    queryKey: ['/api/', dispatchHistoryParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(true)) {
        return {
          rows: [
            {
              title: 'Itinerary1',
              date: '07-29-2025',
              status: DispatchStatusType.Completed,
              driver: 'John Doe',
              vehicle: '25B32343',
              total: 5,
              deliveries: 11,
            },
            {
              title: 'Itinerary2',
              date: '07-27-2025',
              status: DispatchStatusType.Pending,
              driver: 'Mary Jane Watson',
              vehicle: '25B32343',
              total: 5,
              deliveries: 11,
            },
            {
              title: 'Itinerary3',
              date: '07-24-2025',
              status: DispatchStatusType.InProgress,
              driver: 'Sarah Elizabeth Taylor',
              vehicle: '25B32343',
              total: 5,
              deliveries: 11,
            },
          ],
          page: {
            pageSize: 10,
            totalCnt: 1,
            pageNum: 1,
          },
        };
      } else {
        try {
          return {
            rows: [],
            page: {
              pageSize: 10,
              totalCnt: 1,
              pageNum: 1,
            },
          };
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!dispatchHistoryParams,
  });

  const handleSearch = () => {
    setDispatchHistoryParams({
      startDate: formValues.startDate,
      endDate: formValues.endDate,
      plateNo: formValues.plateNo,
      driverName: formValues.driverName,
    });
  };

  useEffect(() => {
    handleSearch();
  }, []);

  return (
    <CustomFrame name={t('DispatchHistory')} back={false}>
      <section className="w-b-b-r-30">
        <form onSubmit={handleSubmit(handleSearch)}>
          <div className="mb-14 f-c gap-4">
            <div className="f-c gap-[10px]">
              <FromToSelector
                initValue={{
                  start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                  end: dayjs().format('YYYY-MM-DD'),
                }}
                onInit={(startDate, endDate) => {
                  setFormValues((prev) => ({
                    ...prev,
                    startDate: startDate,
                    endDate: endDate,
                  }));
                }}
                onChange={(startDate, endDate) =>
                  setFormValues((prev) => ({
                    ...prev,
                    startDate: startDate,
                    endDate: endDate,
                  }))
                }
              />
              {/* 차량 번호 */}
              <Input
                placeholder={t('VehicleNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: '',
                  }))
                }
              />
              {/* 기사명 */}
              <Input
                placeholder={t('DriverName')}
                value={formValues.driverName}
                {...register('driverName', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                })}
                error={errors.driverName?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    driverName: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    driverName: '',
                  }))
                }
              />
            </div>
            <Button type="submit" variant="bt_primary" label={t('Search')} />
          </div>
        </form>

        <CommonTable
          columns={columns}
          data={dispatchHistoryPage?.rows || []}
          isPagination={true}
          isCheckbox={false}
          onRowClick={() => openDispatchDetails(() => {})}
          tbodyclassName="cursor-pointer"
          customPageSize={dispatchHistoryPage?.page.pageSize ?? 0}
          totalCount={dispatchHistoryPage?.page.totalCnt ?? 0}
          currentPage={
            dispatchHistoryPage?.page.pageNum
              ? dispatchHistoryPage.page.pageNum + 1
              : 1
          }
          onPageChange={(page: number) => {
            setDispatchHistoryParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
          }}
        />
      </section>
    </CustomFrame>
  );
};

export default DispatchHistory;
