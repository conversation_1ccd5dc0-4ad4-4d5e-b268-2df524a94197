import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import { Switch } from '@/Common/Components/common/Switch';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import { StatisticsType } from '@/types/StatisticsType';

const NotificationSettings = ({ title }: StatisticsType.MenuTabPros) => {
  const { t } = useTranslation();

  return (
    <Tabs.Content
      value={'NotificationSettings'}
      className="
        space-y-10
        [&>article]:py-5
        [&>article]:px-[30px]
        [&>article]:bg-white
        [&>article]:border
        [&>article]:border-gray-6
        [&>article]:rounded-md
        [&>article>div]:py-[14px]
        [&>article>div]:px-5
        [&>article>div]:f-c
        [&>article>div]:gap-5
        [&>article>div]:border-b
        [&>article>div:last-child]:border-b-0
        [&>article>div]:border-gray-6
        [&>article>div>span]:block
        [&>article>div>span]:w-[280px]
      "
    >
      {/*   */}

      {/*   */}
      <article>
        <SearchItemContainer>
          <SearchLabel>{t('ReceiveNotificationsOnTheWeb')}</SearchLabel>
          <Switch label={''} />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('ReceiveNotificationsInTheApp')}</SearchLabel>
          <Switch label={''} />
        </SearchItemContainer>
      </article>

      <article>
        <SearchItemContainer>
          <h2 className="subtitle3">{t('All')}</h2>
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('FaultAlarm')}</SearchLabel>
          <Switch label={''} />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('MaintenanceAlerts')}</SearchLabel>
          <Switch label={''} />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('OtherAlerts')}</SearchLabel>
          <Switch label={''} />
        </SearchItemContainer>
      </article>
    </Tabs.Content>
  );
};

export default NotificationSettings;
