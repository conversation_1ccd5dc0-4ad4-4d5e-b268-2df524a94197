import { useTranslation } from 'react-i18next';
import UseManagementPopup from '@/Pages/Management/Component/UseManagementPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { ChangeEvent, useState, useMemo } from 'react';
import { formatDate } from '@/Common/function/date.ts';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';

interface ServiceStatusData {
  qmnum: string;
  claimType: string;
  operateHours: string;
  ausvn: string;
  ausbs: string;
  name: string;
  check: string;
}

const Service = () => {
  const { t } = useTranslation();

  const { openServiceHistoryPopup, openServiceRegistrationPopup } =
    UseManagementPopup();

  const options = [
    { key: 'CAMPAIGN', value: 'CAMPAIGN' },
    { key: 'PRE_DELIVERY', value: 'PRE_DELIVERY' },
    { key: 'FOC', value: 'FOC' },
    { key: 'PARTS', value: 'PARTS' },
    { key: 'WARRANTY', value: 'WARRANTY' },
  ];

  const [totalCount, setTotalCount] = useState(15); // Mock total count

  const [claimTypeStr, setClaimTypeStr] = useState<string>('CAMPAIGN');

  console.log('claimTypeStr', claimTypeStr);

  const today = formatDate(new Date());

  const [inputValues, setInputValues] = useState({
    qmnum: '',
    ausvnFrom: today,
    ausvnTo: today,
  });

  const [requestParams, setRequestParams] = useState({
    pageNum: 1,
    pageSize: 10,
    qmnum: '',
    claimType: undefined as string | undefined,
    ausvnFrom: today,
    ausvnTo: today,
  });

  const resetInput = (field: string) => {
    setInputValues((prev) => ({
      ...prev,
      [field]: '',
    }));
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setInputValues((prev) => ({
      ...prev,
      ausvnFrom: formatDate(new Date(startDate)),
      ausvnTo: formatDate(new Date(endDate)),
    }));
  };

  const handlePageChange = (newPage: number) => {
    setRequestParams((prev) => ({
      ...prev,
      pageNum: newPage,
    }));
  };

  const handleSearch = () => {
    setRequestParams((prev) => ({
      ...prev,
      ...inputValues,
      claimType: claimTypeStr,
    }));
    console.log(inputValues);
    console.log(requestParams);
  };

  // Mock 데이터
  const mockServiceData: ServiceStatusData[] = useMemo(
    () => [
      {
        qmnum: 'CLM001',
        claimType: 'CAMPAIGN',
        operateHours: '1500',
        ausvn: '2024-01-15',
        ausbs: '2024-01-20',
        name: 'John Smith',
        check: 'view',
      },
      {
        qmnum: 'CLM002',
        claimType: 'WARRANTY',
        operateHours: '2300',
        ausvn: '2024-02-10',
        ausbs: '2024-02-15',
        name: 'Jane Doe',
        check: 'view',
      },
      {
        qmnum: 'CLM003',
        claimType: 'FOC',
        operateHours: '800',
        ausvn: '2024-03-05',
        ausbs: '2024-03-08',
        name: 'Mike Johnson',
        check: 'view',
      },
      {
        qmnum: 'CLM004',
        claimType: 'PARTS',
        operateHours: '3200',
        ausvn: '2024-04-12',
        ausbs: '2024-04-18',
        name: 'Sarah Wilson',
        check: 'view',
      },
      {
        qmnum: 'CLM005',
        claimType: 'PRE_DELIVERY',
        operateHours: '50',
        ausvn: '2024-05-20',
        ausbs: '2024-05-22',
        name: 'David Lee',
        check: 'view',
      },
    ],
    [],
  );

  const columns = [
    {
      header: t('ClaimNumber'),
      accessorKey: 'qmnum',
    },
    {
      header: t('ClaimType'),
      accessorKey: 'claimType',
    },
    {
      header: t('OperatingHour'),
      accessorKey: 'operateHours',
    },
    {
      header: t('FaultOccurrenceDate'),
      accessorKey: 'ausvn',
    },
    {
      header: t('FaultResolutionDate2'),
      accessorKey: 'ausbs',
    },
    {
      header: t('ActionTakenBy'),
      accessorKey: 'name',
    },
    {
      header: t('InspectionDetails'),
      accessorKey: 'check',
      cell: () => (
        <span
          onClick={() => openServiceHistoryPopup()}
          className="blue-underline"
        >
          {t('View')}
        </span>
      ),
    },
  ];

  return (
    <CustomFrame name={t('ServiceStatus')} back={false}>
      <section className="pt-10">
        {/* 필터 선택 */}
        <article className="mb-5 flex items-center justify-between">
          <div className=" flex items-center gap-6">
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('ClaimNumber')}</span>
              <Input
                placeholder={t('ClaimNumber')}
                className="w-[140px]"
                name="qmnum"
                value={inputValues.qmnum}
                onChange={handleInputChange}
                reset={() => resetInput('qmnum')}
              />
            </div>
            <SearchItemContainer>
              <SearchLabel>{t('ClaimType')}</SearchLabel>
              <DropDown
                onChange={(value) => setClaimTypeStr(value.toString())}
                options={options}
                selectedKey={claimTypeStr}
                placeholder={'CAMPAIGN'}
              />
            </SearchItemContainer>
            <div className="flex items-center gap-6">
              <SearchLabel>{t('DateFaultOccurenceDate')}</SearchLabel>
              <FromToSelector
                onChange={(startDate, endDate) =>
                  handleDateRangeChange(startDate, endDate)
                }
              />
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              variant={'bt_primary'}
              label={'Search'}
              onClick={handleSearch}
            />
            <Button variant={'bt_primary'} label={'Print'} />
          </div>
        </article>

        {/* 테이블 버튼 */}
        <Button
          variant={'bt_primary'}
          label={'Register'}
          onClick={() => openServiceRegistrationPopup()}
          className="mb-2"
        />

        {/* 테이블  */}
        <CommonTable
          columns={columns}
          data={mockServiceData}
          isPagination={true}
          customPageSize={requestParams.pageSize}
          currentPage={requestParams.pageNum}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      </section>
    </CustomFrame>
  );
};

export default Service;
