import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { isEmailValid } from '@/utils/regex/validators.ts';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';

interface SendEmailProps {
  onClose: () => void;
}

const SendEmail = ({ onClose }: SendEmailProps) => {
  const { t } = useTranslation();

  const [email, setEmail] = useState('');
  const [touched, setTouched] = useState(false);

  const valid = isEmailValid(email);
  const error =
    touched && email && !valid ? t('NoMatchFoundPleaseCheckndTryAgain') : '';

  return (
    <article>
      <div className="pt-[60px] pb-[50px] px-9 space-y-20">
        {/*  */}
        <h3 className="mb-20 caption1 text-center whitespace-pre">
          {t('WevSentAPasswordResetLinkToTheEmailYouEntered')}
        </h3>

        {/*  */}
        <div>
          <Button
            variant="bt_primary"
            label={t('BackToLogin')}
            className="w-full"
            onClick={onClose}
          />
          <h3 className="mt-[10px] body4 text-gray-8 text-center">
            {t('DidntReceiveTheEmail')}
            <a href="#" className="ml-1 text-semantic-2">
              {t('ResendEmail')}
            </a>
          </h3>
        </div>

        {/*  */}
        <h3 className="caption4 text-gray-10">
          {t(
            'IfTheButtonDoesntWorkProperlyPleaseCopyAndPasteTheLinkBelowIntoYourBrowser',
          )}
        </h3>
      </div>
    </article>
  );
};

export default SendEmail;
