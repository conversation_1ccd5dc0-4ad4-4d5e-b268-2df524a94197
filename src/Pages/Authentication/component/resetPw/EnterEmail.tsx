import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { isEmailValid } from '@/utils/regex/validators.ts';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';

interface EnterEmailProps {
  onClose: () => void;
  onConfirm: () => void;
}

const EnterEmail = ({ onClose, onConfirm }: EnterEmailProps) => {
  const { t } = useTranslation();

  const [email, setEmail] = useState('');
  const [touched, setTouched] = useState(false);

  const valid = isEmailValid(email);
  const error =
    touched && email && !valid ? t('NoMatchFoundPleaseCheckndTryAgain') : '';

  return (
    <article>
      <div className="px-9">
        <SearchItemContainer>
          <SearchLabel>{t('Email')}</SearchLabel>
          <Input
            placeholder={t('EnterEmail')}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onBlur={() => setTouched(true)}
            error={error}
          />
        </SearchItemContainer>

        <div className="w-full mt-[120px] f-c gap-[10px]">
          <Button
            variant="bt_secondary"
            label={t('BackToLogin')}
            className="w-full"
            onClick={onClose}
          />
          <Button
            variant="bt_primary"
            label={t('Next')}
            className="w-full"
            disabled={!valid}
            onClick={() => {
              setTouched(true);
              if (!valid) return;
              onConfirm?.();
            }}
          />
        </div>
      </div>
    </article>
  );
};

export default EnterEmail;
