import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

const LoginAttemptsPopup = ({
  isOpen,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  const { t } = useTranslation();

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        {/*  */}
        <article>
          <h2> {t('SettingHistory')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article>
          <p>
            {t(
              'ThisAccountHasBeenLockedDueToTooManyFailedLoginAttemptsPleaseResetYourPasswordToRegainAccess',
            )}
          </p>
        </article>
      </section>
    </Layout>
  );
};

export default LoginAttemptsPopup;
