import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Link2Icon } from '@radix-ui/react-icons';
import { useToast } from '@/Common/useToast.tsx';
import UseQAPopup from '@/Pages/Q&A/Component/UseQAPopup';
import { CustomFrame } from '@/Pages/CustomFrame';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';

const QA = () => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';
  const { toast } = useToast();
  const navigate = useNavigate();
  const { openQADeletePopup } = UseQAPopup();

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 검색 조건 상태
  const [qnaType, setQnaType] = useState('ALL');
  const [searchQnaType, setSearchQnaType] = useState('ALL');
  const [title, setTitle] = useState('');
  const [searchTitle, setSearchTitle] = useState('');

  // 더미 QnA 리스트 데이터
  const [qnaListData, setQnaListData] = useState([
    {
      no: '1',
      inquiry: 'Account',
      title: 'Title 1',
      author: 'Manager',
      date: '2024-07-15',
      status: t('Pending'),
      fileName: [],
    },
    {
      no: '2',
      inquiry: 'Service',
      title: 'Title 2',
      author: 'Manager',
      date: '2024-07-14',
      status: t('CompletedQA'),
      fileName: ['file.pdf'],
    },
  ]);

  // 선택 변경 핸들러
  const handleSelectionChange = (selectedRows: { no: string }[]) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => row.no ?? '0'));
    }
  };

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (value: string) => {
    setQnaType(value);
  };

  // 검색 버튼 클릭 핸들러
  const handleSearch = () => {
    setSearchQnaType(qnaType);
    setSearchTitle(title);
    setPageNum(1);
    // 실제 구현시 여기에 데이터 필터링 코드 추가 가능
  };

  // 페이지네이션 핸들러
  const handlePageChange = (newPage: number) => {
    setPageNum(newPage);
  };

  // 등록 버튼
  const routeRegistration = () => {
    navigate('/qna-registration');
  };

  // 테이블 리렌더링 key
  const [tableKey, setTableKey] = useState(0);

  // 삭제 핸들러 (더미 동작)
  const handleDeleteQAs = (close: () => void) => {
    toast({ types: 'warning', description: t('QADeleted') });
    close();
    setSelectedCheck([]);
    setTableKey((prev) => prev + 1);
  };

  // 드롭다운 옵션
  const [qnaTypeOptions] = useState([
    { key: t('AllInquiryType'), value: 'ALL' },
    { key: t('Account'), value: 'Account' },
    { key: t('ServicePeriod'), value: 'Service' },
    { key: t('Data'), value: 'Data' },
    { key: t('Others'), value: 'Other' },
    { key: t('HW'), value: 'Hw' },
    { key: t('SW'), value: 'Sw' },
  ]);

  // 테이블 컬럼 정의
  const columns = [
    {
      header: t('No'),
      accessorKey: 'no',
    },
    {
      header: t('InquiryType'),
      accessorKey: 'inquiry',
    },
    {
      header: t('Title'),
      accessorKey: 'title',
      cell: ({
        row,
      }: {
        row: { original: { title: string; fileName: string[] } };
      }) => (
        <div className="f-c gap-1">
          {row.original.fileName && row.original.fileName.length > 0 && (
            <Link2Icon width={18} height={18} />
          )}
          <span>{row.original.title}</span>
        </div>
      ),
    },
    {
      header: t('Author'),
      accessorKey: 'author',
    },
    {
      header: t('PostedDate'),
      accessorKey: 'date',
    },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ row }: { row: { original: { status: string } } }) => {
        const value = row.original.status;
        const isPending = value === t('Pending');
        return (
          <span className={isPending ? 'text-semantic-4' : ''}>{value}</span>
        );
      },
    },
  ];

  return (
    <CustomFrame name={t('QNA')} back={false}>
      <section className="w-b-b-r-30">
        {/* 검색 조건 */}
        <article className="mb-[22px] f-c gap-4">
          <div className="w-fit f-c gap-[10px]">
            <DropDown
              size="md"
              placeholder={t('All')}
              options={qnaTypeOptions}
              onChange={(value) => handleDropdownChange(value.toString())}
            />
            <Input
              placeholder={t('SearchTC')}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              widthSize="lg"
            />
          </div>
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={handleSearch}
          />
        </article>

        {/* 버튼 */}
        <article className="mb-[10px] f-c-e gap-[10px]">
          <Button
            variant={'bt_primary_sm'}
            label={'Delete'}
            onClick={() => openQADeletePopup(handleDeleteQAs)}
            disabled={selectedCheck.length === 0}
          />
          <Button
            variant={'bt_primary_sm'}
            label={'Register'}
            onClick={routeRegistration}
          />
        </article>

        {/* 테이블 */}
        <CommonTable
          key={tableKey}
          columns={columns}
          data={qnaListData}
          isPagination
          isCheckbox
          onSelectionChange={handleSelectionChange}
          currentPage={pageNum}
          customPageSize={pageSize}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          onRowClick={(row) => navigate(`/qna-details/${row.no}`)}
          tdclassName="cursor-pointer"
        />
      </section>
    </CustomFrame>
  );
};

export default QA;
