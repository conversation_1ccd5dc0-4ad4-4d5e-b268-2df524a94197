import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/Common/Components/common/Button';

const ViewDetails = ({
  onAnswerClick,
  setQnaDetail,
}: {
  onAnswerClick: () => void;
  setQnaDetail: React.Dispatch<
    React.SetStateAction<{
      qnaType?: string;
      author?: string;
      postedDate?: string;
      status?: string;
      title?: string;
      detail?: string;
      questionFileName?: string[];
      answerFileName?: string[];
      answerQnaId?: string;
      answerAuthor?: string;
      answerPostedDt?: string;
      answerLastDt?: string;
      answer?: string;
    } | null>
  >;
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { qnaId } = useParams<{ qnaId: string }>();
  const langType = i18n.language === 'en' ? 'US' : 'KR';

  // 더미 데이터
  const [detail, setDetail] = useState({
    qnaType: 'Service Period',
    author: 'Manager',
    postedDate: '2024-07-15',
    status: 'Pending',
    title: 'What happens when my service period expires?',
    detail: 'What happens to the features when the service period ends?',
    questionFileName: ['질문이미지1.jpg', 'Carta_55.pdf'],
    answerFileName: ['답변이미지1.png', 'Carta_55.pdf'],
    answerQnaId: '11',
    answerAuthor: 'Manager',
    answerPostedDt: '2024-07-16',
    answerLastDt: '2024-07-20',
    answer:
      'When the service period expires, access to key features will be restricted, and only a renewal notice will be displayed after login.',
  });

  // prop 전달
  useEffect(() => {
    setQnaDetail(detail);
  }, [detail, setQnaDetail]);

  // 이미지 미리보기 더미 (이미지 파일만)
  const [imagePreviews] = useState<{
    [fileName: string]: string;
  }>({
    '질문이미지1.jpg':
      'https://dummyimage.com/400x200/cccccc/000000.png&text=질문이미지1',
    '답변이미지1.png':
      'https://dummyimage.com/400x200/cccccc/000000.png&text=답변이미지1',
  });

  // Q&A 리스트 더미 데이터
  const qnaList = [
    {
      qnaId: '10',
      title: 'How do I reset my password?',
      author: '<EMAIL>',
      postedDate: '2024-07-14 10:00:00',
    },
    {
      qnaId: '11',
      title: 'What happens when my service period expires?',
      author: '<EMAIL>',
      postedDate: '2024-07-15 11:21:00',
    },
    {
      qnaId: '12',
      title: 'Can I upgrade my plan?',
      author: '<EMAIL>',
      postedDate: '2024-07-16 12:30:00',
    },
  ];

  // 파일 다운로드(더미)
  const handleFileDownload = (fileName: string) => {
    alert(fileName + ' 다운로드');
  };

  const currentIndex = qnaList.findIndex(
    (q) => String(q.qnaId) === String(qnaId),
  );
  const prevQna = currentIndex > 0 ? qnaList[currentIndex - 1] : null;
  const nextQna =
    currentIndex !== -1 && currentIndex < qnaList.length - 1
      ? qnaList[currentIndex + 1]
      : null;

  return (
    <section className="w-b-b-r-30">
      {/* 문의 내역 */}
      <article>
        {/*  */}
        <div className="f-s-b">
          <div>
            <h2 className="mb-3 subtitle2">{detail?.title}</h2>
            <div
              className="
                text-divider
                [&_h3]:subtitle5 [&_p]:body3
                [&_p]:text-gray-10"
            >
              <div>
                <h3>{t('QAType')}</h3>
                <p>{detail?.qnaType}</p>
              </div>
              <div>
                <h3>{t('Author')}</h3>
                <p>{detail?.author}</p>
              </div>
              <div>
                <h3>{t('PostedDate')}</h3>
                <p>{detail?.postedDate}</p>
              </div>
              <div>
                <h3>{t('Status')}</h3>
                <p>{detail?.status}</p>
              </div>
            </div>
          </div>
          <div className="f-c gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Delete'}
              onClick={() =>
                navigate(`/qna-edit/${qnaId}`, { state: { detail } })
              }
            />
            <Button
              variant={'bt_primary'}
              label={'Edit'}
              onClick={onAnswerClick}
            />
          </div>
        </div>
        {/*  */}
        <div className="mt-[30px] mb-10 py-5 f-c gap-5 border-y border-gray-6">
          <h3 className="subtitle4">{t('AttatchFile')}</h3>
          {detail?.questionFileName?.map((fileName) => {
            const isPdf = /\.pdf$/i.test(fileName);
            return isPdf ? (
              <div
                key={fileName}
                onClick={() => handleFileDownload(fileName)}
                className="caption3 blue-underline"
              >
                {fileName}
              </div>
            ) : null;
          })}
        </div>
      </article>
      {/* 문의 내용 */}
      <article>
        <p
          dangerouslySetInnerHTML={{ __html: detail?.detail ?? '' }}
          className="mb-[30px] body2 whitespace-pre"
        />
        {detail?.questionFileName?.map((fileName) => {
          const isImage = /\.(jpg|jpeg|bmp|png|gif)$/i.test(fileName);
          return isImage && imagePreviews[fileName] ? (
            <img key={fileName} src={imagePreviews[fileName]} alt={fileName} />
          ) : null;
        })}
      </article>

      {/* 답변 내역 */}
      <article className="mt-[70px] pt-[70px] border-t border-gray-10">
        <div>
          <h2 className="mb-3 subtitle2 text-secondary-6">{t('Answer')}</h2>
          <div
            className="
                text-divider
                [&_h3]:subtitle5 [&_p]:body3
                [&_p]:text-gray-10"
          >
            <div>
              <h3>{t('Author')}</h3>
              <p>{detail?.answerAuthor}</p>
            </div>
            <div>
              <h3>{t('PostedDate')}</h3>
              <p>{detail?.answerPostedDt}</p>
            </div>
            <div>
              <h3>{t('LastUpdated')}</h3>
              <p>{detail?.answerLastDt}</p>
            </div>
          </div>
          <div></div>
        </div>
        {/*  */}
        <div className="mt-[30px] mb-10 py-5 f-c gap-5 border-y border-gray-6">
          <h3 className="subtitle4">{t('AttatchFile')}</h3>
          {detail?.answerFileName?.map((fileName) => {
            const isPdf = /\.pdf$/i.test(fileName);
            return isPdf ? (
              <div
                key={fileName}
                onClick={() => handleFileDownload(fileName)}
                className="caption3 blue-underline"
              >
                {fileName}
              </div>
            ) : null;
          })}
        </div>
      </article>
      {/* 답변 내용 */}
      <article>
        <p
          dangerouslySetInnerHTML={{ __html: detail?.answer ?? '' }}
          className="mb-[30px] body2 whitespace-pre"
        />
        {detail?.answerFileName?.map((fileName) => {
          const isImage = /\.(jpg|jpeg|bmp|png|gif)$/i.test(fileName);
          return isImage && imagePreviews[fileName] ? (
            <img key={fileName} src={imagePreviews[fileName]} alt={fileName} />
          ) : null;
        })}
      </article>

      {/* 이전 글 / 다음 글 */}
      <div className="mt-20">
        {/* 이전 글 Q&A */}
        <div
          onClick={
            prevQna
              ? () => navigate(`/qna-details/${prevQna.qnaId}`)
              : undefined
          }
          className="py-5 px-[30px] f-c-b border-y border-gray-6"
        >
          <div className="f-c gap-[50px]">
            <h3 className="w-[75px] subtitle5">{t('Previous')}</h3>
            <p
              style={{
                color: prevQna ? undefined : '#cccccc',
              }}
              className="body3"
            >
              {prevQna ? prevQna.title : t('ThereIsNoPreviousArticle')}
            </p>
          </div>
        </div>

        {/* 다음 글 Q&A */}
        <div
          onClick={
            nextQna
              ? () => navigate(`/qna-details/${nextQna.qnaId}`)
              : undefined
          }
          className="py-5 px-[30px] f-c-b border-b border-gray-6"
        >
          <div className="f-c gap-[50px]">
            <h3 className="w-[75px] subtitle5">{t('NextT')}</h3>
            <p
              style={{
                color: nextQna ? undefined : '#cccccc',
              }}
              className="body3"
            >
              {nextQna ? nextQna.title : t('ThereIsNoNextArticle')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ViewDetails;
