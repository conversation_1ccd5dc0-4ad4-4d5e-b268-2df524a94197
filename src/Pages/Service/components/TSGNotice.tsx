import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Tabs } from '@radix-ui/themes';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CommonTable from '@/Common/Components/common/CommonTable';
import { ServiceType } from '@/types/ServiceType';

const TsgNotice = () => {
  const { t } = useTranslation();

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);

  const handleSelectionChange = (
    selectedRows: ServiceType.TsgNoticeTable[],
  ) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => row.modelGroup ?? '0'));
    }
  };

  const columns: ColumnDef<ServiceType.TsgNoticeTable>[] = [
    {
      accessorKey: 'check',
      size: 200,
      header: () => (
        <CustomColumnHeader className={'w-full justify-start'}>
          {t('ModelGroup')}
        </CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell className={'justify-start'}>
          {row.original.modelGroup}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'type',
      size: 80,
      header: () => <CustomColumnHeader>{t('Type')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.type}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'spn',
      size: 80,
      header: () => <CustomColumnHeader>{t('HCESPNN')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.spn}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'fmi',
      size: 80,
      header: () => <CustomColumnHeader>{t('FMI')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.fmi}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'tsg',
      size: 200,
      header: () => <CustomColumnHeader>{t('TSGTitle')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.tsg}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'read',
      size: 80,
      header: () => <CustomColumnHeader>{t('ViewNum')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.read}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'remark',
      size: 80,
      header: () => <CustomColumnHeader>{t('RemarkEng')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.remark}</CustomColumnDataCell>
      ),
    },
  ];
  const data = [
    {
      check: true,
      modelGroup: 'Forklift (22/25/30B-9F)',
      type: 'MASTER',
      spn: 'M253',
      fmi: '31',
      tsg: 'Handbrake Jammed',
      read: '6',
      remark: '-',
    },
    {
      check: true,
      modelGroup: 'Forklift (22/25/30B-9F)',
      type: 'QC',
      spn: 'M253',
      fmi: '31',
      tsg: 'Handbrake Jammed',
      read: '5',
      remark: '-',
    },
    {
      check: true,
      modelGroup: 'Forklift (22/25/30B-9F)',
      type: 'MASTER',
      spn: 'M253',
      fmi: '31',
      tsg: 'Handbrake Jammed',
      read: '4',
      remark: '-',
    },
    {
      check: true,
      modelGroup: 'Forklift (22/25/30B-9F)',
      type: 'QC',
      spn: 'M253',
      fmi: '31',
      tsg: 'Handbrake Jammed',
      read: '8',
      remark: '-',
    },
  ];

  return (
    <Tabs.Content value={t('TSGBoard')} className={'space-y-10'}>
      <SearchItemContainer className={'w-full flex-wrap gap-y-4 justify-start'}>
        <div className="flex items-center mr-4">
          <SearchLabel className="mr-2.5">{t('Type')}</SearchLabel>
          <DropDown
            options={[]}
            placeholder={t('All')}
            onChange={() => {}}
            className={'w-30'}
          />
        </div>

        <div className="flex items-center mr-4">
          <SearchLabel className="mr-2.5">{t('ModelGroup')}</SearchLabel>
          <DropDown
            options={[]}
            placeholder={t('All')}
            onChange={() => {}}
            className={'w-30'}
          />
        </div>

        <div className="flex items-center mr-4">
          <SearchLabel className="mr-2.5">{t('Model')}</SearchLabel>
          <DropDown
            options={[]}
            placeholder={t('All')}
            onChange={() => {}}
            className={'w-30'}
          />
        </div>

        <div className="flex items-center mr-4">
          <SearchLabel className="mr-2.5">(HCE)SPN</SearchLabel>
          <Input placeholder={'(HCE)SPN'} />
        </div>

        <div className="flex items-center">
          <SearchLabel className="mr-2.5">{t('FMI')}</SearchLabel>
          <Input placeholder={'FMI'} />
        </div>
      </SearchItemContainer>
      <SearchItemContainer className="flex justify-between">
        <div className="flex items-center mr-4">
          <SearchLabel className="mr-2.5">{t('TSGTitle')}</SearchLabel>
          <Input
            placeholder={t('TSGTitle')}
            width={'w-[300px] sm:w-[400px] md:w-[523px]'}
          />
        </div>
        <div className="flex flex-wrap gap-2 mt-2 sm:mt-0">
          <Button variant={'bt_primary'} label={'Search'} />
          <Button variant={'bt_primary'} label={'Print'} />
        </div>
      </SearchItemContainer>

      <div className={'space-y-2'}>
        <div className="flex items-center justify-between">
          <Button
            variant={'bt_primary'}
            label={'PDFDownload'}
            disabled={selectedCheck.length === 0}
            className={'w-fit'}
          />
          <div className="flex flex-wrap gap-2">
            <DropDown
              options={[]}
              placeholder={t('Remark')}
              onChange={() => {}}
              className={'w-30'}
            />
            <DropDown
              options={[]}
              placeholder={t('DescendingOrder')}
              onChange={() => {}}
              className={'w-30'}
            />
          </div>
        </div>
        <CommonTable
          columns={columns}
          data={data}
          isPagination={true}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
        />
      </div>
    </Tabs.Content>
  );
};

export default TsgNotice;
