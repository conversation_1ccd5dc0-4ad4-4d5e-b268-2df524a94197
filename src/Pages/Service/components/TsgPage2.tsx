import { useTranslation } from 'react-i18next';
import SearchLabel from '@/Common/Components/etc/SearchLabel';

const TsgPage2 = () => {
  const { t } = useTranslation();

  return (
    <div className={'space-y-5'}>
      <SearchLabel className={'w-[78px]'}>
        {t('TroubleShootingProcedure')}
      </SearchLabel>
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('AdvancePreparation')}
        </SearchLabel>
        <div className="justify-start items-center gap-4 flex flex-wrap">
          <img
            className="w-[490px] h-[490px]"
            src="https://placehold.co/490x490"
          />
          <div className="w-[490px] h-[490px] bg-white" />
          <div className="w-[490px] h-[490px] bg-white" />
        </div>
        <div>
          <SearchLabel>
            준비물 : 노트북, ZIVAN Pc Can Console 프로그램, PCAN Drive, PCAN
            Drive와 장비의 CAN H/L 라인을 연결할 수 있는 케이블, 완속충전기
          </SearchLabel>
        </div>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <div>
          <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
            {t(' Step1')}
          </SearchLabel>
        </div>
        <div>
          <SearchLabel>고장 여부 확인</SearchLabel>
        </div>
        <div>
          <SearchLabel>
            Step 1-1. HCESPN 8083 / FMI 9 고장코드 발생 여부
          </SearchLabel>
        </div>
        <div className="justify-start items-center gap-4 flex flex-wrap">
          <img
            className="w-[490px] h-[490px]"
            src="https://placehold.co/490x490"
          />
          <div className="w-[490px] h-[490px] bg-white" />
          <div className="w-[490px] h-[490px] bg-white" />
        </div>
      </div>
    </div>
  );
};

export default TsgPage2;
