import { useTranslation } from 'react-i18next';
import SearchLabel from '@/Common/Components/etc/SearchLabel';

const TsgPage1 = () => {
  const { t } = useTranslation();

  return (
    <div className={'space-y-5'}>
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('ComponentLocation')}
        </SearchLabel>
        <div className="justify-start items-center gap-4 flex flex-wrap">
          <img
            className="w-[490px] h-[490px]"
            src="https://placehold.co/490x490"
          />
          <div className="w-[490px] h-[490px] bg-white" />
          <div className="w-[490px] h-[490px] bg-white" />
        </div>
        <div className="text-xl font-semibold">
          OBC #1 MASTER(L)의 B+ 전원(CN-910 #1)은 BMS의 B+ 전원(CN-911 #6)과
          연결됨
          <br />
          OBC #1 MASTER(L)의 IG 전원(CN-910 #3)은 BMS의 IG 전원(CN-911 #5)과
          연결됨
          <br />
          OBC #1 MASTER(L)의 접지(CN-920 #9, CN-910 #2)는 차체에 Ground되어 있음
          <br />
          OBC #1 MASTER(L)의 CAN H 신호(CN-910 #7)은 OBC #2 SLAVE(R)의 CAN H
          신호(CN-919 #1) 및 BMS CAN H 신호(CN-911 #3)로 연결됨
          <br />
          OBC #1 MASTER(L)의 CAN L 신호(CN-910 #8)은 OBC #2 SLAVE(R)의 CAN L
          신호(CN-919 #2) 및 BMS CAN L 신호(CN-911 #9)로 연결됨
        </div>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('Status')}
        </SearchLabel>
        <div className="text-xl font-semibold">
          OBC #1 MASTER(L)의 B+ 전원(CN-910 #1)은 BMS의 B+ 전원(CN-911 #6)과
          연결됨
          <br />
          OBC #1 MASTER(L)의 IG 전원(CN-910 #3)은 BMS의 IG 전원(CN-911 #5)과
          연결됨
        </div>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('AddtionInformation')}
        </SearchLabel>
        <div className="justify-start items-center gap-4 flex flex-wrap">
          <img
            className="w-[490px] h-[490px]"
            src="https://placehold.co/490x490"
          />
          <div className="w-[490px] h-[490px] bg-white" />
          <div className="w-[490px] h-[490px] bg-white" />
        </div>
        <div className="text-xl font-semibold leading-7">
          예상 가능한 고장 원인 Component는 1. <br /> OBC 등이 있을 수 있으며,
          상세 내용은 T/S Step 및 Step Guide 참고할 것
        </div>
      </div>
    </div>
  );
};

export default TsgPage1;
