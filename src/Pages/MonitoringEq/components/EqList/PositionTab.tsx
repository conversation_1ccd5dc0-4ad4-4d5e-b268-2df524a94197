import { useTranslation } from 'react-i18next';
import { Fragment, useEffect, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { MapEngine } from '@/types';
import { DemoTest } from '@/types';
import dayjs from 'dayjs';
import usePopup from '@/hooks/usePopup';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import { Button } from '@/Common/Components/common/Button';
import ZoomController from '@/Common/Components/map/ZoomController';
import EqSingleMarker from '@/Common/Components/Marker/EqSingleMarker';
import EqPositionInfoWindow from '@/Common/Components/eqWindow/EqPositionInfoWindow';
import { MapRandomData } from '@/Common/constants/Maps.ts';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import { Polyline } from '@/logiMaps/react/google/Poly/Polyline';
import arrowDown from '@/assets/images/ic/28/arrow_down.svg';
import TimelineBadge from '@/assets/images/svg/30/TimelineBadge.tsx';
import { EquipmentType } from '@/types/EquipmentType';
import { GeneralPolyline } from '@/logiMaps/react/general/Poly';
import TrackingMarker from '@/Common/Components/Marker/TrackingMarker';
import { generatePositionTabData } from '@/helpers/equipmentDetailDataGenerator';

enum ViewMode {
  default = 0,
  location = 1,
  route = 2,
}

type TimelineSegment = {
  tagName: string;
  address: string;
  arrivalTime: string;
  estimatedTime: string;
  completed: boolean;
  path: { lat: number; lng: number }[];
};

type TimelineItem = {
  segments: TimelineSegment[];
};

type WorkingHistoryParams = {
  equipmentId: string;
  selectedDate: string;
};

type WorkingHistoryPage = {
  rows: WorkingHistoryRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type PreOperationRow = {
  dispatchStatus: 'before';
  title: {
    name: string;
  };
  driver: null;
  drivingStats: {
    label:
      | 'Average Speed'
      | 'Overspeed Count'
      | 'Harsh Braking Count'
      | 'Harsh Acceleration Count'
      | 'Sudden Start Count';
    value: string;
  }[];
  currentLocation: {
    lat: number;
    lng: number;
  };
  timelineList: null;
};

type InOperationRow = {
  dispatchStatus: 'inOperation';
  title: {
    name: string;
    score: string;
  };
  driver: null;
  drivingStats: {
    label:
      | 'Average Speed'
      | 'Overspeed Count'
      | 'Harsh Braking Count'
      | 'Harsh Acceleration Count'
      | 'Sudden Start Count';
    value: string;
  }[];
  currentLocation: {
    lat: number;
    lng: number;
  };
  timelineList: TimelineItem[];
};

type PostOperationRow = {
  dispatchStatus: 'completed';
  title: {
    name: string;
    timeRange: string;
    totalTime: string;
    totalDistance: string;
    score: string;
  };
  driver: {
    name: string;
    phone: string;
    email: string;
    type: string;
  };
  drivingStats: {
    label:
      | 'Driving Distance'
      | 'Driving Time'
      | 'Average Speed'
      | 'Visited Locations'
      | 'Overspeed Count'
      | 'Harsh Braking Count'
      | 'Harsh Acceleration Count'
      | 'Sudden Start Count';
    value: string;
  }[];
  currentLocation: {
    lat: number;
    lng: number;
  };
  timelineList: TimelineItem[];
};

type WorkingHistoryRow = PreOperationRow | InOperationRow | PostOperationRow;

/**
 * 운행 이력
 */
const PositionTab = ({
  equipmentId,
}: {
  equipmentId: string;
  isElectric: boolean;
}) => {
  const { t } = useTranslation();
  const { openExportPopup } = usePopup();

  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const mapSizeRef = useRef<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });
  const maxZoomLevel = 21;
  const minZoomLevel = 3;
  const defaultLevel = 17;
  const zoomLevelRef = useRef(defaultLevel);
  const [zoomGauge, setZoomGauge] = useState(defaultLevel);
  // 선택된 날짜 상태 관리
  const [selectedDate, setSelectedDate] = useState(
    dayjs().format('YYYY-MM-DD'),
  );

  /** Query */
  const [workingHistoryParams, setWorkingHistoryParams] =
    useState<WorkingHistoryParams>({
      equipmentId: equipmentId,
      selectedDate: selectedDate,
    });

  const { data: workingHistoryPage } = useQuery({
    queryKey: ['/api/', workingHistoryParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generatePositionTabData();
      } else {
        try {
          const result: WorkingHistoryPage = {
            rows: [],
            page: {
              pageSize: 10,
              totalCnt: 0,
              pageNum: 0,
            },
          };
          return result;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: {
      rows: [],
      page: {
        pageSize: 10,
        totalCnt: 0,
        pageNum: 0,
      },
    },
    enabled: true,
  });

  /** Event Listener */

  // 지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    setMapAdapter(generalMapAdapter);
  };

  //지도 스케일 변경
  const handleMapZoomChanged = (zoom: number) => {
    zoomLevelRef.current = zoom;
    setZoomGauge(zoom);
  };

  //지도 화면 사이즈 체크
  const handleMapSizeChanged = (width: number, height: number) => {
    mapSizeRef.current.width = width;
    mapSizeRef.current.height = height;
  };

  //검색 버튼 클릭
  const onSearchClick = () => {
    setWorkingHistoryParams((prev) => ({
      ...prev,
      selectedDate: selectedDate,
    }));
  };

  /** Function */

  // 운행 중 / 지난 경로 정보 펼침 상태 관리
  const [isOpenArr, setIsOpenArr] = useState([false, false, false]);
  const toggleIsOpen = (idx: number) => {
    setIsOpenArr((prev) => prev.map((val, i) => (i === idx ? !val : false)));
  };

  return (
    <div>
      <SearchItemContainer
        style={{ justifyContent: 'space-between' }}
        className="mb-5"
      >
        <div className="f-c gap-[10px]">
          <DaySelector
            initValue={dayjs().format('YYYY-MM-DD')}
            onChange={(date) => {
              setSelectedDate(date);
            }}
          />
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={onSearchClick}
          />
        </div>
        <Button
          variant={'bt_tertiary'}
          label={'Download'}
          onClick={() => {
            openExportPopup();
          }}
        />
      </SearchItemContainer>

      <div className="space-y-4">
        {workingHistoryPage.rows.length === 0 ? (
          <div className="w-b-b-r-30 body1 text-gray-10 text-center">
            {t('NoHistory')}
          </div>
        ) : (
          workingHistoryPage.rows.map((row, idx) => (
            <div className="w-b-b-r" key={row.dispatchStatus}>
              {/* --- expand open bar --- */}
              <div
                onClick={() => toggleIsOpen(idx)}
                className="py-[18px] px-6 f-c-b cursor-pointer"
              >
                <div className="f-c subtitle3 [&>p]:px-4 [&>p:first-child]:pl-0 [&>p:last-child]:pr-0 [&>p]:border-r [&>p:last-child]:border-0 [&>p]:border-gray-6 [&>p]:leading-[14px]">
                  {/* before (배차 전) */}
                  {row.dispatchStatus === 'before' && (
                    <h3 className="subtitle3">{t('Location')}</h3>
                  )}
                  {/* inOperation (운행 중 / 현재 경로) */}
                  {row.dispatchStatus === 'inOperation' && (
                    <>
                      <span className="w-3 h-3 mr-[-4px] bg-semantic-1 rounded-full" />
                      <p>In Operation</p>
                      <p>{row.title.name}</p>
                    </>
                  )}
                  {/* completed (운행 완료 / 지난 경로) */}
                  {row.dispatchStatus === 'completed' && (
                    <>
                      <p>{row.title.name}</p>
                      <p>{row.title.timeRange}</p>
                      <p>{row.title.totalTime}</p>
                      <p>{row.title.totalDistance}</p>
                    </>
                  )}
                </div>
                <div className="f-c gap-4">
                  {/* inOperation (운행 중 / 현재 경로), completed (운행 완료 / 지난 경로) */}
                  {(row.dispatchStatus === 'inOperation' ||
                    row.dispatchStatus === 'completed') && (
                    <div className="f-c gap-4">
                      <div className="w-fit py-[6px] px-4 f-c gap-2 bg-semantic-1-1 border border-semantic-1 rounded-full">
                        <p className="caption2">{t('SafeDrivingScore')}</p>
                        <span className="subtitle6 text-semantic-1">
                          {row.title.score}
                        </span>
                      </div>
                    </div>
                  )}
                  <img
                    src={arrowDown}
                    alt="arrowDown"
                    className={`h-a-m ${isOpenArr[idx] ? 'rotate-180' : ''}`}
                  />
                </div>
              </div>
              {/* --- expand details --- */}
              {/* inOperation (운행 중 / 현재 경로), completed (운행 완료 / 지난 경로) 일 때만 나옴 */}
              {isOpenArr[idx] && (
                <div
                  className={`border-t border-gray-6 transition-all duration-300 ease-in-out overflow-hidden px-6 pb-6`}
                  style={{ maxHeight: 600, opacity: 1, pointerEvents: 'auto' }}
                >
                  <div className="h-full relative">
                    <GeneralMap
                      mapSource={MapEngine.source()}
                      className={`w-full ${row.dispatchStatus === 'before' ? 'h-[450px]' : 'h-[250px]'}`}
                      id={`location-general-${idx}`}
                      maxZoom={maxZoomLevel}
                      minZoom={minZoomLevel}
                      defaultZoom={defaultLevel}
                      defaultCenter={row.currentLocation}
                      onInitMap={() => {}}
                      onZoomChanged={() => {}}
                      onBoundsChanged={() => {}}
                    >
                      {/* 경로 모드 */}
                      {row.dispatchStatus !== 'before' && (
                        <>
                          {row.timelineList?.map((item, idx2) =>
                            item.segments.map((segment, idx3) =>
                              segment.completed ? (
                                <GeneralPolyline
                                  key={`tracking-l-${idx2}-${idx3}`}
                                  path={segment.path}
                                  width={10}
                                  color={'#FF5900'}
                                />
                              ) : (
                                <GeneralPolyline
                                  key={`tracking-l-${idx2}-${idx3}`}
                                  path={segment.path}
                                  width={10}
                                  color={'#A5A5A5'}
                                />
                              ),
                            ),
                          )}
                          {row.timelineList?.map((item, idx2) =>
                            item.segments.map((segment, idx3) => {
                              if (segment.path.length < 1) return null;
                              return (
                                <TrackingMarker
                                  key={`tracking-m-${idx2}-${idx3}`}
                                  id={`tracking-m-${idx2}-${idx3}`}
                                  latlng={segment.path[segment.path.length - 1]}
                                  deliveryStatus={
                                    segment.completed ? 'after' : 'before'
                                  }
                                />
                              );
                            }),
                          )}
                        </>
                      )}
                      <EqSingleMarker
                        id={`marker-${idx}`}
                        latlng={row.currentLocation}
                      />
                      <EqPositionInfoWindow
                        id={`info1-${idx}`}
                        position={row.currentLocation}
                        pixelOffset={[0, -4]}
                        routeInfo={{ date: '2024-07-21' }}
                      />
                      <ZoomController
                        right="right-5"
                        bottom="bottom-5"
                        plus={() => {}}
                        minus={() => {}}
                      />
                    </GeneralMap>
                    {(row.dispatchStatus === 'inOperation' ||
                      row.dispatchStatus === 'completed') && (
                      <div>
                        <div className="w-full f-c border border-gray-6">
                          <div className="py-[10px] px-5 space-y-1 border-r border-gray-6 [&>div]:f-c [&>div]:gap-3 [&_h2]:w-[155px] [&_h2]:caption3 [&_h2]:text-gray-10 [&_p]:w-[60px] [&_p]:body5 [&_p]:text-right">
                            {row.drivingStats.map((stat, i) => (
                              <div key={i}>
                                <h2>{stat.label}</h2>
                                <p>{stat.value}</p>
                              </div>
                            ))}
                          </div>
                          <div className="w-full">
                            <div className="timeline-wk">
                              <div className="max-h-[250px] card-wrap overflow-x-scroll">
                                {row.timelineList?.map((item, idx2) => (
                                  <Fragment key={idx2}>
                                    <div className="card-section">
                                      <div className="time-con">
                                        <div className="info time-info">
                                          {item.segments.map((seg, sidx) => {
                                            const isArrived =
                                              sidx < item.segments.length - 1;
                                            return (
                                              <div
                                                key={sidx}
                                                className="time-bar"
                                                data-estimatedtime={
                                                  seg.estimatedTime
                                                }
                                              >
                                                <TimelineBadge
                                                  arrived={isArrived}
                                                />
                                                <h2>{seg.tagName}</h2>
                                                <p>{seg.address}</p>
                                                <span>{seg.arrivalTime}</span>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      </div>
                                    </div>
                                  </Fragment>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default PositionTab;
