import { useTranslation } from 'react-i18next';
import { Button } from '@/Common/Components/common/Button';

const DealerServiceCenter = () => {
  const { t } = useTranslation();

  return (
    <section>
      <h2 className="mb-[30px] subtitle3">
        {t('DealerServiceCenterInformation')}
      </h2>

      {/* 딜러 정보 */}
      <article className="w-b-b-r-30 info-layout mb-10">
        <h3 className="f-c-b">
          {t('DealerInformation')}
          <Button variant={'bt_tertiary_sm'} label={t('Edit')} />
        </h3>
        <div>
          <p>{t('DealerCompanyName')}</p>
          <span>{'<PERSON> Smith'}</span>
        </div>
        <div>
          <p>{t('PhoneNumber')}</p>
          <span>{'+82 1012345678'}</span>
        </div>
        <div>
          <p>{t('Address')}</p>
          <span>
            {
              'Room 217, 58 Wangsimni-ro, Seongdong-gu, Seoul, Republic of Korea'
            }
          </span>
        </div>
      </article>

      {/* 서비스 센터 정보 */}
      <article className="w-b-b-r-30 info-layout mb-10">
        <h3 className="f-c-b">
          {t('ServiceCenterInformation')}
          <Button variant={'bt_tertiary_sm'} label={t('Edit')} />
        </h3>
        <div>
          <p>{t('Name')}</p>
          <span>{'John Smith'}</span>
        </div>
        <div>
          <p>{t('Address')}</p>
          <span>{'550, Dongdaegu-ro, Dong-gu, Daegu, Republic of Korea'}</span>
        </div>
        <div>
          <p>{t('PhoneNumber')}</p>
          <span>{'+82 1012345678'}</span>
        </div>
      </article>
    </section>
  );
};

export default DealerServiceCenter;
