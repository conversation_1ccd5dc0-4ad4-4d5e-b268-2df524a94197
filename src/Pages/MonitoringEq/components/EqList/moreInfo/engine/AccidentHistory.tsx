import { useTranslation } from 'react-i18next';
import CommonTable from '@/Common/Components/common/CommonTable';

const AccidentHistory = () => {
  const { t } = useTranslation();

  const columns = [
    { header: t('AccidentTime'), accessorKey: 'time' },
    { header: t('Location'), accessorKey: 'location' },
    { header: t('ResponseStatus'), accessorKey: 'status' },
  ];

  const data = [
    {
      time: '06-01-2025',
      location: '115 Old Hwy 11, Lumberton, MS 39455, USA',
      status: 'Responding',
    },
  ];

  return (
    <section className="w-b-b-r-30">
      <h2 className="mb-[30px] subtitle3">{t('AccidentHistory')}</h2>

      <CommonTable
        columns={columns}
        data={data}
        isPagination={true}
        isCheckbox={false}
      />
    </section>
  );
};

export default AccidentHistory;
