import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import usePopup from '@/hooks/usePopup';
import { EquipmentType } from '@/types/EquipmentType';

const ExpendableHistoryTab = ({ equipmentId }: { equipmentId: string }) => {
  const { t } = useTranslation();

  const { openExportPopup } = usePopup();

  const columns: ColumnDef<EquipmentType.ExpendableHistoryTabProps>[] = [
    {
      accessorKey: 'date',
      header: () => <div>{t('TaskDate')}</div>,
      size: 152,
      cell: ({ row }) => <div>{row.original.date}</div>,
    },
    {
      accessorKey: 'item',
      header: () => <div>{t('ConsumableItem')}</div>,
      size: 280,
      cell: ({ row }) => <div>{row.original.item}</div>,
    },
    {
      accessorKey: 'type',
      header: () => <div>{t('TaskType')}</div>,
      size: 112,
      cell: ({ row }) => <div>{row.original.type}</div>,
    },
    {
      accessorKey: 'setting',
      header: () => <div>{t('ConfiguredValue')}</div>,
      size: 112,
      cell: ({ row }) => <div>{row.original.setting}</div>,
    },
    {
      accessorKey: 'mileage',
      header: () => <div>{t('Mileage')}</div>,
      size: 112,
      cell: ({ row }) => <div>{row.original.mileage}</div>,
    },
    {
      accessorKey: 'exchange',
      header: () => <div>{t('MaintenanceDetails')}</div>,
      size: 72,
      cell: () => <div className="blue-underline">{t('View')}</div>,
    },
  ];

  //구분 필터
  const [taskSelKey, setTaskSelKey] = useState(t('TaskType'));
  const [consumableSelKey, setConsumableTypeSelKey] = useState(
    t('ConsumableItem'),
  );

  /** Query */
  const { data: taskOptions } = useQuery({
    queryKey: ['expendable:common/gubun'],
    queryFn: async () => {
      try {
        return [
          { key: t('All'), value: 'all' },
          { key: t('ConsumableReplacement'), value: 'consumable' },
          { key: t('IntervalUpdate'), value: 'update' },
        ];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: [{ key: t('All'), value: 'ALL' }],
    enabled: true,
  });

  const { data: consumableOptions } = useQuery({
    queryKey: ['expendable:common/gubun'],
    queryFn: async () => {
      try {
        return [{ key: t('All'), value: 'all' }];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: [{ key: t('All'), value: 'ALL' }],
    enabled: true,
  });

  const expendableHistoryData = {
    pageNum: 0,
    pageSize: 0,
    totalCnt: 0,
    rows: [],
  };

  /** useEffect */
  useEffect(() => {
    handleSearchClick();
  }, []);

  /** Event */

  //검색 버튼 클릭
  const handleSearchClick = () => {
    console.log('handleSearchClick');
  };

  return (
    <div className="w-b-b-r-30">
      <h2 className="mb-[30px] subtitle3">{t('ConsumablesHistory')}</h2>

      <SearchItemContainer
        style={{ justifyContent: 'space-between' }}
        className="mb-[56px] gap-[10px]"
      >
        <div className="f-c gap-[10px]">
          <DropDown
            options={taskOptions}
            placeholder={taskSelKey}
            onSelKey={setTaskSelKey}
          />
          <DropDown
            options={consumableOptions}
            placeholder={consumableSelKey}
            onSelKey={setConsumableTypeSelKey}
          />
          <FromToSelector />
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={handleSearchClick}
          />
        </div>
        <Button
          variant={'bt_tertiary'}
          label={'Download'}
          onClick={() => {
            openExportPopup();
          }}
        />
      </SearchItemContainer>

      <div>
        <CommonTable
          data={expendableHistoryData.rows}
          columns={columns}
          isPagination={true}
          customPageSize={expendableHistoryData.pageSize}
          totalCount={expendableHistoryData.totalCnt}
          currentPage={expendableHistoryData.pageNum}
        />
      </div>
    </div>
  );
};

export default ExpendableHistoryTab;
