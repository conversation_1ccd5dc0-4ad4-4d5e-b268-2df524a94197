import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import Input from '@/Common/Components/common/Input';
import { useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType';

const ExchangeTermPopup = ({
  isOpen,
  onClose,
  row,
}: EquipmentType.ExchangeTermPopupProps) => {
  const { t } = useTranslation();

  //교환 주기 필터
  const [term, setTerm] = useState(row.term);
  const [isTermValid, setIsTermValid] = useState(true);

  const handleSave = () => {
    //숫자
    if (/^[a-zA-Z0-9]+$/.test(term) == false) {
      setIsTermValid(false);
    } else {
      setIsTermValid(true);
    }
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('ChangingReplacementCycle')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <SearchItemContainer>
          <SearchLabel>{t('Cycle')}</SearchLabel>
          <Input
            placeholder={term}
            onChange={(e) => {
              setTerm(e.target.value);
            }}
          ></Input>
        </SearchItemContainer>
        <div>{!isTermValid && <p>{t('InvalidValue')}</p>}</div>
        <div>
          <Button variant={'bt_primary'} label={'Cancel'} onClick={onClose} />
          <Button variant={'bt_primary'} label={'Save'} onClick={handleSave} />
        </div>
      </div>
    </Layout>
  );
};

export default ExchangeTermPopup;
