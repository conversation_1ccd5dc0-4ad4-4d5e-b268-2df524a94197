import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import Radio, { RadioOption } from '@/Common/Components/common/Radio.tsx';
import SearchLabel from '@/Common/Components/etc/SearchLabel.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer.tsx';
import { ColumnDef } from '@tanstack/react-table';
import additem from '@/assets/images/popup/add_item.png';
import receiver from '@/assets/images/popup/add_receiver.png';
import DropDown from '@/Common/Components/common/DropDown.tsx';
import Input from '@/Common/Components/common/Input.tsx';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { EquipmentType } from '@/types/EquipmentType.ts';

const ExpendableAlarmPopup = ({
  onClose,
  isOpen,
  equipmentId,
  modelName,
  plateNo,
  row,
}: EquipmentType.ExpendableAlarmPopupProps) => {
  const { t, i18n } = useTranslation();

  const [selectedValue] = useState<string>('2');

  const [sendPageList, setSendPageList] = useState<
    EquipmentType.AlarmReceiverColumnProps[]
  >([]);

  //권한 필터
  const [permisionSelKey, setPermisionSelKey] = useState('-');
  //국가 필터
  const [countrySelKey, setCountrySelKey] = useState('All');
  //이름 필터
  const [name, setName] = useState('');
  //전화번호 필터
  const [mobileNo, setMobileNo] = useState('');
  //이메일 필터
  const [email, setEmail] = useState('');
  //내용
  const [content, setContent] = useState('');

  const [selected, setSelected] = useState<string>('0');
  const options: RadioOption[] = [
    { value: '0', label: 'EMail' },
    { value: '1', label: 'App Push' },
  ];

  const AlarmReceiverTableColumn: ColumnDef<EquipmentType.AlarmReceiverColumnProps>[] =
    [
      {
        size: 72,
        header: () => <div>{t('No')}</div>,
        accessorKey: 'no',
        cell: ({ row }) => <div>{row.original.no}</div>,
      },
      {
        size: 112,
        header: () => <div>{t('Permission')}</div>,
        accessorKey: 'permission',
        cell: ({ row }) => <div>{row.original.permission}</div>,
      },
      {
        size: 112,
        header: () => <div>{t('Country')}</div>,
        accessorKey: 'country',
        cell: ({ row }) => <div>{row.original.country}</div>,
      },
      {
        size: 152,
        header: () => <div>{t('PhoneNumber')}</div>,
        accessorKey: 'mobile',
        cell: ({ row }) => <div>{row.original.mobile}</div>,
      },
      {
        size: 192,
        header: () => <div>{t('EMail')}</div>,
        accessorKey: 'email',
        cell: ({ row }) => <div>{row.original.email}</div>,
      },
      {
        size: 192,
        header: () => <div>{t('Name')}</div>,
        accessorKey: 'name',
        cell: ({ row }) => <div>{row.original.name}</div>,
      },
      {
        size: 92,
        header: () => <div>{t('Delete')}</div>,
        accessorKey: 'delete',
        cell: ({ row }) => (
          <Button
            variant={'bt_primary'}
            label={'Delete'}
            onClick={() => {
              handleDelClick(row.original);
            }}
          />
        ),
      },
    ];

  /** Query */

  const { data: permisionOptions } = useQuery({
    queryKey: ['alarm:common/permision', i18n.language === 'en'],
    queryFn: async () => {
      try {
        return [{ key: '-', value: '-' }];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: [],
    enabled: true,
  });

  const countryOptions: { key: string; value: string }[] = [
    {
      key: '',
      value: '',
    },
  ];

  const mutationAlarmSendCallQuery = useMutation({
    mutationFn: async (params: any) => {
      // try {
      //   const response =
      //     await equipmentAlarmApi.apiUpdateEquipmentAlarmSendCall({
      //       equipmentMaintenanceAlarmSendCallDTO: params,
      //     });
      //   return response;
      // } catch (error) {
      //   console.error('API 호출 에러:', error);
      //   throw error;
      // }
      return null;
    },
    onSuccess: () => {
      // toast({
      //   types: 'success',
      //   description: t('InformationHasBeenUpdated'),
      // });
      // onConfirm();
    },
    onError: (error) => {
      console.error('API 호출 에러:', error);
      throw error;
    },
  });

  const handleAddClick = () => {
    if (name.length == 0) {
      alert(`${t('InvalidValue')} (name)`);
      return;
    }
    if (/^\d*$/.test(mobileNo) == false) {
      alert(`${t('InvalidValue')} (mobile)`);
      return;
    }
    if (
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email) == false
    ) {
      alert(`${t('InvalidValue')} (email)`);
      return;
    }

    setSendPageList((prevItems) => [
      ...prevItems,
      {
        no: (prevItems.length + 1).toString(),
        permission:
          permisionOptions.find((option) => option.key === permisionSelKey)
            ?.value ?? '',
        userId: '',
        country:
          countryOptions.find((option) => option.key === countrySelKey)
            ?.value ?? '',
        mobile: mobileNo ?? '',
        email: email ?? '',
        name: name ?? '',
        delete: 'Delete',
      },
    ]);
  };

  const handleDelClick = (row: EquipmentType.AlarmReceiverColumnProps) => {
    setSendPageList((prevItems) => {
      //해당 값을 가진 항목을 삭제
      const filteredItems = prevItems.filter((item) => item.no !== row.no);
      //삭제 후, 번호 갱신
      return filteredItems.map((item, index) => {
        //추가된 항목만 대상 (delete 가능 항목)
        if (item.delete.length > 0) {
          return {
            ...item,
            no: (index + 1).toString(),
          };
        }
        return item;
      });
    });
  };

  const handleSend = () => {
    let gubun = '';
    switch (selectedValue) {
      case '2':
        gubun = 'E'; //E-Email
        break;
      case '1':
        gubun = 'S'; //S-SMS
        break;
      case '3':
        gubun = 'B'; //B-Both
        break;
      case '4':
        gubun = 'A'; //A-App
        break;
    }

    if (gubun.length == 0) {
      return;
    }

    if (content.length == 0) {
      return;
    }

    mutationAlarmSendCallQuery.mutate({
      gubun: gubun,
      message: content,
      equipmentId: equipmentId,
      alarmClass: row.mantAlarm,
      targets: sendPageList.map((item) => {
        return {
          mobile: item.mobile,
          userId: item.userId,
          country: item.country,
          email: item.email,
        };
      }),
    });
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('AlarmMessageNotice')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <div>
          <SearchItemContainer>
            <SearchLabel>{t('NotificationMethod')}</SearchLabel>
            <Radio
              options={options}
              value={selected}
              onValueChange={setSelected}
            />
          </SearchItemContainer>
          <hr />
          <SearchItemContainer>
            <SearchLabel>{t('EquipmentInfo')}</SearchLabel>
            <SearchLabel>
              {t('Model')} {/* 모델 */}
            </SearchLabel>
            <SearchLabel>{modelName}</SearchLabel>
            <SearchLabel>
              {t('MachineID')} {/* 호기 */}
            </SearchLabel>
            <SearchLabel>{plateNo}</SearchLabel>
            <SearchLabel>{t('Mileage')}</SearchLabel>
            <SearchLabel>{row.mileage}</SearchLabel>
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('Maintenance')}</SearchLabel>
            <SearchLabel>
              {t('Item')} {/* 소모품 항목 */}
            </SearchLabel>
            <SearchLabel>{row.item}</SearchLabel>
            <SearchLabel>
              {t('ReplaceDate')} {/* 교체 날짜 */}
            </SearchLabel>
            <SearchLabel>{row.lastDate}</SearchLabel>
            <SearchLabel>
              {t('HoursonItem')} {/* Hours on Item */}
            </SearchLabel>
            <SearchLabel>{"'-'"}</SearchLabel>
          </SearchItemContainer>
          <div>
            <SearchLabel>{t('RecipientC')}</SearchLabel>
          </div>
          <div>
            <CommonTable
              data={sendPageList}
              columns={AlarmReceiverTableColumn}
              isPagination={false}
            />
          </div>
          <div>
            <SearchLabel>{t('AddRecipient')}</SearchLabel>
          </div>
          <div>
            <img src={additem} />
            <button>
              <img src={receiver} onClick={handleAddClick} />
            </button>
            <DropDown
              options={permisionOptions}
              placeholder={permisionSelKey}
              onSelKey={(key) => {
                setPermisionSelKey(key);
              }}
            ></DropDown>
            <DropDown
              options={countryOptions}
              placeholder={countrySelKey}
              onSelKey={(key) => {
                setCountrySelKey(key);
              }}
            ></DropDown>
            <SearchItemContainer>
              <SearchLabel>{t('Name')}</SearchLabel>
              <Input
                placeholder={t('Name')}
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                }}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Phone')}</SearchLabel>
              <Input
                placeholder={t('Phone')}
                value={mobileNo}
                onChange={(e) => {
                  setMobileNo(e.target.value);
                }}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('EMail')}</SearchLabel>
              <Input
                placeholder={t('EMail')}
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                }}
              />
            </SearchItemContainer>
          </div>
          <div>
            <SearchLabel>{t('Content')}</SearchLabel>
            <textarea
              onChange={(e) => {
                setContent(e.target.value);
              }}
              value={`Check: ${modelName}(${plateNo})\n${row.item}`}
            />
          </div>
        </div>

        <div>
          <div>
            <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
            <Button
              variant={'bt_primary'}
              label={'Send'}
              onClick={handleSend}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ExpendableAlarmPopup;
