import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Cross1Icon } from '@radix-ui/react-icons';

const TruckPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData = [
    { label: t('FuelEfficiency'), value: '25 km/L' },
    { label: t('AerodynamicDragCoefficient'), value: '0.30' },
    { label: t('TirePressure'), value: '35 psi' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[780px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('VehicleSpecifications')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          {/* 요약 */}
          <div className="mb-10 flex flex-col">
            <div className="mb-4 subtitle4">
              {t('DrivingResistanceFactors')}
            </div>
            <SummaryData details={summaryData} fs="lg" />
          </div>

          {/* 버튼 */}
          <div className="f-je">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default TruckPopup;
