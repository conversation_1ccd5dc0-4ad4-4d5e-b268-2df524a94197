import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Cross1Icon } from '@radix-ui/react-icons';

const RegularVehiclePopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData1 = [
    { label: t('FuelEfficiency'), value: '25 km/L' },
    { label: t('AerodynamicDragCoefficient'), value: '0.30' },
    { label: t('TirePressure'), value: '35 psi' },
    { label: t('DriveType'), value: '4-Wheel Drive (4WD)' },
  ];

  const summaryData2 = [
    { label: t('Height'), value: '12ft 3in' },
    { label: t('Width'), value: '15ft 3in' },
    { label: t('Length'), value: '15ft 3in' },
    { label: t('TireDiameter'), value: '22.5 in' },
    { label: t('TireWidth'), value: '295 mm' },
    { label: t('HazardousMaterialsType'), value: 'Explosive, Gas, Flammable' },
    { label: t('NumberOfAxles'), value: '3' },
    { label: t('NumberOfWheels'), value: '10' },
    { label: t('NumberOfTrailers'), value: '12' },
    { label: t('KingpinToRearAxleFtM'), value: '134' },
    { label: t('LiftingEquipmentInstalled'), value: 'Yes' },
    { label: t('RefrigerationUnitInstalled'), value: 'Yes' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[780px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('VehicleSpecifications')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article className="p-[30px]">
          {/* 요약 */}
          <div className="mb-5 pb-5 flex flex-col border-b border-gray-4">
            <div className="mb-4 subtitle4">
              {t('DrivingResistanceFactors')}
            </div>
            <SummaryData details={summaryData1} fs="lg" />
          </div>

          <div className="mb-10 flex flex-col">
            <div className="mb-4 subtitle4">{t('TruckConfiguration')}</div>
            <SummaryData details={summaryData2} fs="lg" />
          </div>

          {/* 버튼 */}
          <div className="f-je">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default RegularVehiclePopup;
