import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import { PopupProps } from '@/types';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchLabel from '@/Common/Components/etc/SearchLabel.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer.tsx';
import { ColumnDef } from '@tanstack/react-table';
import Dropdown from '@/Common/Components/common/DropDown.tsx';
import Input from '@/Common/Components/common/Input.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType.ts';

const AlarmCodePopup = ({ onClose, isOpen }: PopupProps) => {
  const { t } = useTranslation();

  const alarmCodeTableColumn: ColumnDef<EquipmentType.AlarmCodeTableColumnProps>[] =
    [
      {
        size: 72,
        header: () => <div>{t('SPN')}</div>,
        accessorKey: 'spn',
        cell: ({ row }) => <div>{row.original.spn}</div>,
      },
      {
        size: 72,
        header: () => <div>{t('Type')}</div>,
        accessorKey: 'type',
        cell: ({ row }) => <div>{row.original.type}</div>,
      },
      {
        size: 72,
        header: () => <div>{t('FMI')}</div>,
        accessorKey: 'fmi',
        cell: ({ row }) => <div>{row.original.fmi}</div>,
      },
      {
        size: 52,
        header: () => <div>{t('Maker')}</div>,
        accessorKey: 'maker',
        cell: ({ row }) => <div>{row.original.maker}</div>,
      },
      {
        size: 52,
        header: () => <div>{t('Severity')}</div>,
        accessorKey: 'level',
        cell: ({ row }) => <div>{row.original.level}</div>,
      },
      {
        size: 328,
        header: () => <div>{t('DescriptionENG')}</div>,
        accessorKey: 'enExplain',
        cell: ({ row }) => (
          <div className=" text-wrap">{row.original.enExplain}</div>
        ),
      },
      {
        size: 328,
        header: () => <div>{t('DescriptionKOR')}</div>,
        accessorKey: 'koExplain',
        cell: ({ row }) => (
          <div className=" text-wrap">{row.original.koExplain}</div>
        ),
      },
      {
        size: 72,
        header: () => <div className=" text-wrap">{t('FaultCode2')}</div>,
        accessorKey: 'reviewCode',
        cell: ({ row }) => (
          <div className=" text-wrap">{row.original.reviewCode}</div>
        ),
      },
    ];

  //SPN
  const [spn, setSpn] = useState('');
  //FMI
  const [fmi, setFmi] = useState('');
  //고장 심각도
  const [severitySelKey, setSeveritySelKey] = useState('All');
  //설명
  const [desc, setDesc] = useState('');

  /** Query */
  const severityOptions: { key: string; value: string }[] = [
    { key: 'All', value: 'ALL' },
  ];

  const [, setAlarmCodeZipParams] = useState({
    spn: '',
    fmi: '',
    desc: '',
    level: '',
    pageNum: 0,
    pageSize: 0,
  });

  const alarmCodeZipData: {
    pageNum: number;
    pageSize: number;
    totalCnt: number;
    rows: EquipmentType.AlarmCodeTableColumnProps[];
  } = {
    pageNum: 0,
    pageSize: 10,
    totalCnt: 2,
    rows: [],
  };

  //검색 버튼 클릭
  const handleSearch = () => {
    console.log('검색 버튼 클릭');
    setAlarmCodeZipParams({
      spn: spn,
      fmi: fmi,
      desc: desc,
      level:
        severityOptions.find((option) => option.key === severitySelKey)
          ?.value ?? '',
      pageNum: 1,
      pageSize: 5,
    });
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('AlarmCodeCollection')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <div className={''}>
          <SearchItemContainer className={''}>
            <SearchLabel className={'w-10'}>{t('SPN')}</SearchLabel>
            <Input
              placeholder={'SPN'}
              onChange={(e) => {
                setSpn(e.target.value);
              }}
            />
            <SearchLabel className={'w-10'}>{t('FMI')}</SearchLabel>
            <Input
              placeholder={'FMI'}
              onChange={(e) => {
                setFmi(e.target.value);
              }}
            />
            <SearchLabel className={'w-24'}>{t('Severity')}</SearchLabel>
            <Dropdown
              options={severityOptions}
              placeholder={severitySelKey}
              onSelKey={setSeveritySelKey}
            />
            <SearchLabel>{t('Description')}</SearchLabel>
            <Input
              placeholder={t('Description')}
              onChange={(e) => {
                setDesc(e.target.value);
              }}
            />
            <SearchItemContainer className={'justify-end flex-1'}>
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
              />
            </SearchItemContainer>
          </SearchItemContainer>

          <CommonTable
            data={alarmCodeZipData.rows}
            columns={alarmCodeTableColumn}
            isPagination={true}
            customPageSize={alarmCodeZipData.pageSize}
            totalCount={alarmCodeZipData.totalCnt}
            currentPage={alarmCodeZipData.pageNum}
            onPageChange={(page: number) => {
              setAlarmCodeZipParams((prevState) => ({
                ...prevState,
                pageNum: page,
              }));
            }}
          />
        </div>
      </div>
    </Layout>
  );
};

export default AlarmCodePopup;
