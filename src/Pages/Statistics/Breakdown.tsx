import { Button } from '@/Common/Components/common/Button';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import useDimension from '@/hooks/useDimension.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import BreakdownLineGraph from '@/Pages/Statistics/components/breakdown/BreakdownLineGraph.tsx';
import BreakdownStatistics from '@/Pages/Statistics/components/breakdown/BreakdownStatistics.tsx';
import ExpendableStatistics from '@/Pages/Statistics/components/breakdown/ExpendableStatistics.tsx';
import FMEAChart from '@/Pages/Statistics/components/breakdown/FMEAChart.tsx';
import ModelBreakdownAlarm from '@/Pages/Statistics/components/breakdown/ModelBreakdownAlarm.tsx';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const Breakdown = () => {
  const { t } = useTranslation();

  const [inputValues, setInputValues] = useState({
    fleetSeqNo: 'ALL',
  });

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    console.log(value);
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Mock 플릿 드롭다운 데이터
  const fleetSelectData = [
    { key: '전체', value: 'ALL' },
    { key: 'Fleet A', value: '1' },
    { key: 'Fleet B', value: '2' },
    { key: 'Fleet C', value: '3' },
  ];

  const [, setBreakdownData] = useState({
    totalCnt: 0,
    pendingCnt: 0,
    responseCnt: 0,
    completedCnt: 0,
  });

  const handleTotalAlarmData = (data: {
    totalCnt?: number;
    pendingCnt?: number;
    responseCnt?: number;
    completedCnt?: number;
  }) => {
    setBreakdownData({
      totalCnt: data.totalCnt ?? 0,
      pendingCnt: data.pendingCnt ?? 0,
      responseCnt: data.responseCnt ?? 0,
      completedCnt: data.completedCnt ?? 0,
    });
  };

  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(2열), md: 태블릿(2열), sm: 작은 태블릿(1열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 1, h: 18, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 18, static: false },
      { i: 'c', x: 0, y: 1, w: 1, h: 27.5, static: false },
      { i: 'd', x: 1, y: 1, w: 1, h: 30, static: false },
      { i: 'e', x: 0, y: 2, w: 1, h: 32.5, static: false },
      { i: 'f', x: 1, y: 2, w: 1, h: 30, static: false },
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 1, h: 18, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 18, static: false },
      { i: 'c', x: 0, y: 1, w: 1, h: 27.5, static: false },
      { i: 'd', x: 1, y: 1, w: 1, h: 30, static: false },
      { i: 'e', x: 0, y: 2, w: 1, h: 32.5, static: false },
      { i: 'f', x: 1, y: 2, w: 1, h: 30, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 1, h: 18, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 18, static: false },
      { i: 'c', x: 0, y: 1, w: 1, h: 27.5, static: false },
      { i: 'd', x: 1, y: 1, w: 1, h: 30, static: false },
      { i: 'e', x: 0, y: 2, w: 1, h: 32.5, static: false },
      { i: 'f', x: 1, y: 2, w: 1, h: 30, static: false },
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 1, h: 18, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 18, static: false },
      { i: 'c', x: 0, y: 1, w: 1, h: 27.5, static: false },
      { i: 'd', x: 1, y: 1, w: 1, h: 30, static: false },
      { i: 'e', x: 0, y: 2, w: 1, h: 32.5, static: false },
      { i: 'f', x: 1, y: 2, w: 1, h: 30, static: false },
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 2, md: 2, sm: 1, xs: 1 };

  const { width } = useDimension();
  const gridWidth = width > 1024 ? width - 280 : width - 72;

  return (
    <CustomFrame name={t('FaultMaintenanceAnalysis')}>
      <div className={'py-3 space-y-4'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer>
            <SearchLabel>{t('Fleet')}</SearchLabel>
            <DropDown
              onChange={(value) =>
                handleDropdownChange('fleetSeqNo', value.toString())
              }
              options={fleetSelectData}
              placeholder={t('All')}
            />
          </SearchItemContainer>
          <Button variant={'bt_primary'} label={'Download'} />
        </SearchItemContainer>
      </div>
      <ResponsiveGridLayout
        className="layout px-4"
        layouts={layouts}
        breakpoints={breakpoints}
        cols={cols}
        isDroppable={false}
        isDraggable={false}
        rowHeight={1}
        width={gridWidth}
        margin={[12, 12]}
        containerPadding={[12, 8]}
      >
        <div className={'p-2 flex flex-col justify-between'} key={'a'}>
          <SearchLabel>{t('FaultAnalysis')}</SearchLabel>
          <BreakdownStatistics
            fleetValue={inputValues.fleetSeqNo.toString()}
            totalAlarm={handleTotalAlarmData}
          />
        </div>

        <div className={'p-2 flex flex-col justify-between'} key={'b'}>
          <SearchLabel>{t('MaintenanceAnalysis')}</SearchLabel>
          <ExpendableStatistics />
        </div>
        <div className={'p-2'} key={'c'}>
          <ModelBreakdownAlarm fleetValue={inputValues.fleetSeqNo.toString()} />
        </div>
        <div className={'p-2'} key={'d'}>
          {/*<ExpendableStatistics />*/}
          <BreakdownLineGraph
            title={t('MaintenanceExpiredItemTrends')}
            data={[]}
          />
        </div>
        <div className={'p-2'} key={'e'}>
          <FMEAChart fleetValue={inputValues.fleetSeqNo.toString()} />
        </div>
        <div className={'p-2'} key={'f'}>
          <BreakdownLineGraph
            title={t('MaintenanceReachedItemTrends')}
            data={[]}
          />
        </div>
      </ResponsiveGridLayout>
    </CustomFrame>
  );
};

export default Breakdown;
