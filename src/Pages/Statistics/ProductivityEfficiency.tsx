import { Button } from '@/Common/Components/common/Button';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import useDimension from '@/hooks/useDimension.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import useStatisticsPopup from '@/Pages/Statistics/components/popup/useStatisticsPopup.tsx';
import AverageWorkTime from '@/Pages/Statistics/components/ProductivityEfficiency/AverageWorkTime.tsx';
import EqOperationRate from '@/Pages/Statistics/components/ProductivityEfficiency/EQOperationRate.tsx';
import JobRestTime from '@/Pages/Statistics/components/ProductivityEfficiency/JobRestTime.tsx';
import StatModelWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/StatModelWorkTimeRanking.tsx';
import WorkerRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkerRanking.tsx';
import WorkerRestTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkerRestTimeRanking.tsx';
import WorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkTimeRanking.tsx';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

import StatMachineNoWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/StatMachineNoWorkTimeRanking.tsx';

const ProductivityEfficiency = () => {
  const { t } = useTranslation();

  const [inputValues, setInputValues] = useState({
    fleetSeqNo: 'ALL',
  });

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    console.log(value);
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(4열), md: 태블릿(2열), sm: 작은 태블릿(1열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 1, h: 30, static: false }, // EqOperationRate (높이 증가: 30 -> 35)
      { i: 'b', x: 1, y: 0, w: 1, h: 30, static: false }, // AverageWorkTime (높이 증가: 30 -> 35)
      { i: 'c', x: 2, y: 0, w: 2, h: 20, static: false }, // WorkTimeRanking
      { i: 'd', x: 2, y: 1, w: 2, h: 10, static: false }, // JobRestTime (높이 감소: 20 -> 15)
      { i: 'e', x: 0, y: 3, w: 2, h: 20, static: false }, // StatModelWorkTimeRanking
      { i: 'f', x: 2, y: 3, w: 2, h: 20, static: false }, // EqWorkTimeRanking
      { i: 'g', x: 0, y: 4, w: 2, h: 20, static: false }, // WorkerRanking
      { i: 'h', x: 2, y: 4, w: 2, h: 20, static: false }, // WorkerRestTimeRanking
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // EqOperationRate (높이 증가)
      { i: 'b', x: 1, y: 0, w: 1, h: 35, static: false }, // AverageWorkTime (높이 증가)
      { i: 'c', x: 0, y: 1, w: 2, h: 20, static: false }, // WorkTimeRanking
      { i: 'd', x: 0, y: 2, w: 2, h: 15, static: false }, // JobRestTime (높이 감소)
      { i: 'e', x: 0, y: 3, w: 2, h: 20, static: false }, // StatModelWorkTimeRanking
      { i: 'f', x: 0, y: 4, w: 2, h: 20, static: false }, // EqWorkTimeRanking
      { i: 'g', x: 0, y: 5, w: 2, h: 20, static: false },
      { i: 'h', x: 0, y: 6, w: 2, h: 20, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'b', x: 0, y: 1, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'c', x: 0, y: 2, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'd', x: 0, y: 3, w: 1, h: 15, static: false }, // 높이 감소
      { i: 'e', x: 0, y: 4, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'f', x: 0, y: 5, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'g', x: 0, y: 6, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'h', x: 0, y: 7, w: 1, h: 20, static: false }, // 그대로 유지
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'b', x: 0, y: 1, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'c', x: 0, y: 2, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'd', x: 0, y: 3, w: 1, h: 15, static: false }, // 높이 감소
      { i: 'e', x: 0, y: 4, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'f', x: 0, y: 5, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'g', x: 0, y: 6, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'h', x: 0, y: 7, w: 1, h: 20, static: false }, // 그대로 유지
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 4, md: 2, sm: 1, xs: 1 };

  const { width } = useDimension();
  const gridWidth = width > 1024 ? width - 280 : width - 72;

  const { openWorkerEqStatisticsPopup } = useStatisticsPopup();

  return (
    <CustomFrame name={t('ProductivityEfficiencyAnalysis')}>
      <div className={'py-3 space-y-8'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer>
            <SearchLabel>{t('Fleet')}</SearchLabel>
            <DropDown
              onChange={(value) =>
                handleDropdownChange('fleetSeqNo', value.toString())
              }
              options={[]}
              placeholder={t('All')}
            />
          </SearchItemContainer>
          <Button
            variant={'bt_primary'}
            label={'Download'}
            onClick={openWorkerEqStatisticsPopup}
          />
        </SearchItemContainer>
      </div>
      <div style={{ overflowX: 'auto', width: '100%' }}>
        <div style={{ minWidth: '1440px' }}>
          <ResponsiveGridLayout
            className="layout px-4"
            layouts={layouts}
            breakpoints={breakpoints}
            cols={cols}
            isDroppable={false}
            isDraggable={false}
            rowHeight={1}
            width={gridWidth}
            margin={[16, 16]}
            containerPadding={[16, 16]}
          >
            <div className={'p-2'} key="a">
              <EqOperationRate title={t('MachineUtilizationRate')} data={[]} />
            </div>
            <div className={'p-2'} key="b">
              <AverageWorkTime title={t('AverageWorkingHours')} data={[]} />
            </div>
            <div className={'p-2'} key={'c'}>
              <WorkTimeRanking workData={[]} />
            </div>
            <div className={'p-2'} key={'d'}>
              <JobRestTime workData={[]} />
            </div>
            <div className={'p-2'} key={'e'}>
              <StatModelWorkTimeRanking eq={[]} />
            </div>
            <div className={'p-2'} key={'f'}>
              <StatMachineNoWorkTimeRanking eq={[]} />
            </div>
            <div className={'p-2'} key={'g'}>
              <WorkerRanking eq={[]} />
            </div>
            <div className={'p-2'} key={'h'}>
              <WorkerRestTimeRanking eq={[]} />
            </div>
          </ResponsiveGridLayout>
        </div>
      </div>
    </CustomFrame>
  );
};

export default ProductivityEfficiency;
