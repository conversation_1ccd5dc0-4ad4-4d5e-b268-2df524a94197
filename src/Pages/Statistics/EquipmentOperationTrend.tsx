import { useTranslation } from 'react-i18next';
import { useMemo, useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import Radio from '@/Common/Components/common/Radio';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import YearSelector from '@/Common/Components/datePicker/YearSelector';
import ECharts from 'echarts-for-react';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CommonTable from '@/Common/Components/common/CommonTable';
import dayjs from 'dayjs';

interface ChartSeriesData {
  name: string;
  type: string;
  data: number[];
}

interface EquipmentOperationTrendTable {
  ename?: string;
  average?: number;
  [key: string]: string | number | undefined;
}

interface DropdownOption {
  key: string;
  value: string;
}

type ValuePiece = Date | null;
type Value = ValuePiece | [ValuePiece, ValuePiece];

const monthsMapping = [
  { key: 'h01', label: 'Jan' },
  { key: 'h02', label: 'Feb' },
  { key: 'h03', label: 'Mar' },
  { key: 'h04', label: 'Apr' },
  { key: 'h05', label: 'May' },
  { key: 'h06', label: 'Jun' },
  { key: 'h07', label: 'Jul' },
  { key: 'h08', label: 'Aug' },
  { key: 'h09', label: 'Sep' },
  { key: 'h10', label: 'Oct' },
  { key: 'h11', label: 'Nov' },
  { key: 'h12', label: 'Dec' },
];

const getLastDayOfMonth = (date: Date) => dayjs(date).daysInMonth();
const generateMonthArray = () => monthsMapping.map((m) => m.label);
const generateMonthDayArray = (lastDay: number) =>
  Array.from({ length: lastDay }, (_, i) => `${i + 1}`);

const OperationTrendOption = {
  legend: { data: [] },
  series: [],
};

const EquipmentOperationTrend = () => {
  const { t } = useTranslation();

  // 필터 상태
  const [filters, setFilters] = useState({
    kubun1: 'L',
    fleetSeqNo: undefined as string | undefined,
    region: 'ALL',
    country: 'ALL',
    sDealer: undefined as string | undefined,
  });

  // 드롭다운 Mock 데이터
  const fleetDropData: DropdownOption[] = useMemo(
    () => [
      { key: t('All'), value: 'ALL' },
      { key: 'A플릿', value: '1' },
      { key: 'B플릿', value: '2' },
      { key: 'C플릿', value: '3' },
    ],
    [t],
  );

  const areaSelectData: DropdownOption[] = useMemo(
    () => [
      { key: t('All'), value: 'ALL' },
      { key: '서울', value: 'SEOUL' },
      { key: '부산', value: 'BUSAN' },
      { key: '대구', value: 'DAEGU' },
    ],
    [t],
  );

  const countrySelectData: DropdownOption[] = useMemo(
    () => [
      { key: t('All'), value: 'ALL' },
      { key: 'KR', value: 'KR' },
      { key: 'JP', value: 'JP' },
      { key: 'US', value: 'US' },
    ],
    [t],
  );

  const fleetDealerData: DropdownOption[] = useMemo(
    () => [
      { key: t('All'), value: 'ALL' },
      { key: 'DealerA', value: 'A' },
      { key: 'DealerB', value: 'B' },
      { key: 'DealerC', value: 'C' },
    ],
    [t],
  );

  const typeOption: DropdownOption[] = useMemo(
    () => [
      { key: t('RegionS'), value: 'L' },
      { key: t('CountryS'), value: 'C' },
    ],
    [t],
  );

  // 기간 타입 및 선택 상태
  const [periodType, setPeriodType] = useState<string>('1');
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date());
  const [selectedYear, setSelectedYear] = useState<Date>(new Date());
  const [filtersPeriodType, setFiltersPeriodType] = useState<string>('1');

  // Mock 차트 및 테이블 데이터
  const [chartTitle, setChartTitle] = useState<(string | undefined)[]>([
    'Seoul',
    'Busan',
    'Daegu',
  ]);

  const [chartData, setChartData] = useState<ChartSeriesData[]>([
    {
      name: 'Seoul',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
    },
    {
      name: 'Busan',
      type: 'line',
      data: [80, 95, 70, 89, 65, 180, 160, 140, 155, 190, 220, 250],
    },
    {
      name: 'Daegu',
      type: 'line',
      data: [60, 75, 55, 68, 50, 140, 130, 110, 125, 150, 170, 200],
    },
  ]);

  const [tableData] = useState<EquipmentOperationTrendTable[]>([
    {
      ename: 'Seoul',
      average: 185,
      h01: 120,
      h02: 132,
      h03: 101,
      h04: 134,
      h05: 90,
      h06: 230,
      h07: 210,
      h08: 182,
      h09: 191,
      h10: 234,
      h11: 290,
      h12: 330,
    },
    {
      ename: 'Busan',
      average: 145,
      h01: 80,
      h02: 95,
      h03: 70,
      h04: 89,
      h05: 65,
      h06: 180,
      h07: 160,
      h08: 140,
      h09: 155,
      h10: 190,
      h11: 220,
      h12: 250,
    },
    {
      ename: 'Daegu',
      average: 115,
      h01: 60,
      h02: 75,
      h03: 55,
      h04: 68,
      h05: 50,
      h06: 140,
      h07: 130,
      h08: 110,
      h09: 125,
      h10: 150,
      h11: 170,
      h12: 200,
    },
  ]);

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);
  const [chartKey, setChartKey] = useState(0);

  // 드롭다운 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value === 'ALL' ? undefined : value,
    }));
  };

  // 날짜 선택 핸들러
  const onMonthSelectorChange = (value: Value) => {
    if (value instanceof Date) {
      setSelectedMonth(value);
    } else if (Array.isArray(value) && value[0] instanceof Date) {
      setSelectedMonth(value[0]);
    } else {
      setSelectedMonth(new Date());
    }
  };

  const onYearSelectorChange = (value: Value) => {
    if (value instanceof Date) {
      setSelectedYear(value);
    }
  };

  // 검색 실행
  const handleSearch = () => {
    setChartKey((prev) => prev + 1);
    setFiltersPeriodType(periodType);

    // Mock 데이터 업데이트 로직
    if (periodType === '1') {
      const dailyData = Array.from(
        { length: getLastDayOfMonth(selectedMonth) },
        () => Math.floor(Math.random() * 100) + 50,
      );
      setChartData([
        { name: 'Daily Operation', type: 'line', data: dailyData },
      ]);
      setChartTitle(['Daily Operation']);
    } else {
      // 연도별 데이터 유지
      setChartData([
        {
          name: 'Seoul',
          type: 'line',
          data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
        },
        {
          name: 'Busan',
          type: 'line',
          data: [80, 95, 70, 89, 65, 180, 160, 140, 155, 190, 220, 250],
        },
      ]);
      setChartTitle(['Seoul', 'Busan']);
    }

    console.log('Search executed with filters:', filters);
  };

  // 일별 매핑 동적 생성
  const daysMapping = useMemo(() => {
    const lastDay = getLastDayOfMonth(selectedMonth);
    return Array.from({ length: lastDay }, (_, i) => ({
      key: `h${(i + 1).toString().padStart(2, '0')}`,
      label: `${i + 1}`,
    }));
  }, [selectedMonth]);

  // 동적으로 컬럼 생성
  const columns: ColumnDef<EquipmentOperationTrendTable>[] = useMemo(() => {
    const baseColumns: ColumnDef<EquipmentOperationTrendTable>[] = [
      {
        accessorKey: 'ename',
        header: () => <CustomColumnHeader>{t('Region')}</CustomColumnHeader>,
        cell: ({ row }) => (
          <CustomColumnDataCell>{row.original.ename}</CustomColumnDataCell>
        ),
      },
    ];

    if (filtersPeriodType === '2') {
      const averageColumns: ColumnDef<EquipmentOperationTrendTable>[] = [
        {
          accessorKey: 'average',
          header: () => (
            <CustomColumnHeader>{t('YearAverage')}</CustomColumnHeader>
          ),
          cell: ({ row }) => (
            <CustomColumnDataCell>{row.original.average}</CustomColumnDataCell>
          ),
        },
      ];
      const monthColumns = monthsMapping.map((month) => ({
        accessorKey: month.key,
        header: () => <CustomColumnHeader>{month.label}</CustomColumnHeader>,
        cell: ({
          row,
        }: {
          row: { original: EquipmentOperationTrendTable };
        }) => (
          <CustomColumnDataCell>
            {row.original[month.key as keyof EquipmentOperationTrendTable]}
          </CustomColumnDataCell>
        ),
      }));
      return [...baseColumns, ...averageColumns, ...monthColumns];
    } else {
      const daysColumns = daysMapping.map((day) => ({
        accessorKey: day.key,
        header: () => <CustomColumnHeader>{day.label}</CustomColumnHeader>,
        cell: ({
          row,
        }: {
          row: { original: EquipmentOperationTrendTable };
        }) => (
          <CustomColumnDataCell>
            {row.original[day.key as keyof EquipmentOperationTrendTable]}
          </CustomColumnDataCell>
        ),
      }));
      return [...baseColumns, ...daysColumns];
    }
  }, [t, filtersPeriodType, daysMapping]);

  const chartOptions = useMemo(() => {
    return {
      ...OperationTrendOption,
      legend: {
        ...OperationTrendOption.legend,
        data: chartTitle,
      },
      series: chartData,
      xAxis: {
        type: 'category',
        axisTick: { show: false },
        splitLine: { show: false },
        data:
          filtersPeriodType === '1'
            ? generateMonthDayArray(getLastDayOfMonth(selectedMonth))
            : generateMonthArray(),
      },
      yAxis: {
        type: 'value',
      },
      tooltip: {
        trigger: 'axis',
      },
    };
  }, [chartData, chartTitle, filtersPeriodType, selectedMonth]);

  const handleSelectionChange = (
    selectedRows: EquipmentOperationTrendTable[],
  ) => {
    setSelectedCheck(selectedRows.map((row) => row.ename ?? '0'));
  };

  const handleRefresh = () => {
    if (selectedCheck.length > 0) {
      // 선택된 항목들에 대한 새로운 Mock 데이터 생성
      const newChartData = selectedCheck.map((name) => ({
        name,
        type: 'line',
        data: Array.from(
          { length: 12 },
          () => Math.floor(Math.random() * 200) + 50,
        ),
      }));

      setChartData(newChartData);
      setChartTitle(selectedCheck);
      setChartKey((prev) => prev + 1);

      console.log('Refreshed data for:', selectedCheck);
    }
  };

  return (
    <CustomFrame name={t('OperationTrend')}>
      <div className={'w-full h-full space-y-10 pt-10'}>
        <div className={'space-y-5'}>
          <SearchItemContainer className={'justify-start  gap-6'}>
            <SearchItemContainer>
              <SearchLabel>{t('Fleet')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('fleetSeqNo', value.toString())
                }
                options={fleetDropData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Region')}</SearchLabel>
              <DropDown
                options={areaSelectData}
                placeholder={t('All')}
                onChange={(value) =>
                  handleDropdownChange('region', value.toString())
                }
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Country')}</SearchLabel>
              <DropDown
                options={countrySelectData}
                placeholder={t('All')}
                onChange={(value) =>
                  handleDropdownChange('country', value.toString())
                }
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('DealerD')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('sDealer', value.toString())
                }
                options={fleetDealerData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Scope')}</SearchLabel>
              <DropDown
                options={typeOption}
                placeholder={t('RegionS')}
                onChange={(value) =>
                  handleDropdownChange('kubun1', value.toString())
                }
                selectedKey={filters.kubun1}
              />
            </SearchItemContainer>
          </SearchItemContainer>
          <SearchItemContainer className={'justify-between'}>
            <Radio
              options={[
                { value: '1', label: t('MonthM') },
                { value: '2', label: t('Year') },
              ]}
              value={periodType}
              onValueChange={setPeriodType}
              className="flex justify-start gap-[80px]"
            />
            {periodType === '1' ? (
              <div className="w-[200px]">
                <MonthSelector
                  value={selectedMonth}
                  onValueChange={onMonthSelectorChange}
                />
              </div>
            ) : (
              <div className="w-[200px]">
                <YearSelector
                  value={selectedYear}
                  onValueChange={onYearSelectorChange}
                />
              </div>
            )}
            <Button
              variant={'bt_primary'}
              label={'Search'}
              onClick={handleSearch}
              className="ml-[-60px]"
            />
            <SearchItemContainer>
              <Button variant={'bt_primary'} label={'Download'} />
            </SearchItemContainer>
          </SearchItemContainer>
        </div>
        <div className={'space-y-[10px]'}>
          <Button
            variant={'bt_primary'}
            label={'Refresh'}
            disabled={selectedCheck.length === 0}
            onClick={handleRefresh}
          />
          <div className={'bg-white h-[368px] pt-10'}>
            <ECharts
              key={chartKey}
              option={chartOptions}
              style={{ height: '308px' }}
            />
          </div>
        </div>
        <div className={'max-h-[548px] overflow-auto'}>
          <CommonTable
            columns={columns}
            data={tableData}
            isPagination={false}
            isCheckbox={true}
            onSelectionChange={handleSelectionChange}
          />
        </div>
      </div>
    </CustomFrame>
  );
};

export default EquipmentOperationTrend;
