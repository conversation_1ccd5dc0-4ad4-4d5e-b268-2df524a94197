import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import YearSelector from '@/Common/Components/datePicker/YearSelector';
import { Button } from '@/Common/Components/common/Button';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import StatisticsTableWithDiv from '@/Pages/Statistics/components/StatisticsTableWithDiv.tsx';
import { useEffect, useState, useMemo } from 'react';
import {
  formatDate,
  getCurrentYear,
  getCurrentYearMonth,
} from '@/Common/function/date.ts';
import dayjs from 'dayjs';
import { Value } from 'react-calendar/src/shared/types.ts';

interface EquipmentUpTimeSearchFilters {
  fleetSeqNo?: number;
  region: string;
  country: string;
  sDealer?: string;
  smodel?: string;
  kubun1: string;
}

interface EquipmentUptimeTableProps {
  time: string;
  engineActiveTime: JSX.Element;
  region: string;
  activeDate: JSX.Element;
  dayCount: string;
}

interface FleetSelectItem {
  key: string;
  value: string;
}

interface StatEngineRunTimeData {
  hours?: number;
  maxH?: number;
  ename?: string;
  days?: number;
  maxD?: number;
}

const EquipmentUptime = () => {
  const { t } = useTranslation();

  const [selected, setSelected] = useState<string>('0');
  const options: RadioOption[] = [
    { value: '0', label: t('MonthM') },
    { value: '1', label: t('Year') },
    { value: '2', label: t('PeriodD') },
  ];

  const columns: ColumnDef<EquipmentUptimeTableProps>[] = [
    {
      accessorKey: 'time',
      size: 112,
      header: () => <CustomColumnHeader>{t('Hours')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.time}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'engineActiveTime',
      size: 560,
      header: () => (
        <CustomColumnHeader>{t('EngineRunningTimeAvg')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell className={'justify-end'}>
          {row.original.engineActiveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'region',
      size: 152,
      header: () => (
        <CustomColumnHeader>{t('RegionCountry')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.region}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'activeDate',
      size: 560,
      header: () => (
        <CustomColumnHeader>{t('WorkingDaysAvg')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell className={'justify-start'}>
          {row.original.activeDate}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'dayCount',
      size: 92,
      header: () => <CustomColumnHeader>{t('Days')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.dayCount}</CustomColumnDataCell>
      ),
    },
  ];

  const EngineActive: React.FC<{ width: string }> = ({ width }) => (
    <div
      className="h-8 -rotate-180 bg-gradient-to-r from-[#FFBB3D] to-[#FF5900]"
      style={{ width: `${width}px` }}
    />
  );

  EngineActive.propTypes = {
    width: PropTypes.string.isRequired,
  };

  const ActiveDate: React.FC<{ width: string }> = ({ width }) => (
    <div
      className="h-[30px] bg-gradient-to-r from-[#002554] to-[#58B4B6]"
      style={{ width: `${width}px` }}
    />
  );

  ActiveDate.propTypes = {
    width: PropTypes.string.isRequired,
  };

  // 검색 필터 상태
  const [filtersRequest, setFilterRequest] =
    useState<EquipmentUpTimeSearchFilters>({
      fleetSeqNo: 1,
      region: 'ALL',
      country: 'ALL',
      sDealer: undefined,
      smodel: undefined,
      kubun1: 'L',
    });

  // Mock 데이터
  const fleetDropData: FleetSelectItem[] = [
    { key: '전체', value: 'ALL' },
    { key: 'Fleet A', value: '1' },
    { key: 'Fleet B', value: '2' },
    { key: 'Fleet C', value: '3' },
  ];

  const areaSelectData: FleetSelectItem[] = [
    { key: '전체', value: 'ALL' },
    { key: 'Asia', value: 'AS' },
    { key: 'Europe', value: 'EU' },
    { key: 'America', value: 'AM' },
  ];

  const countrySelectData: FleetSelectItem[] = [
    { key: '전체', value: 'ALL' },
    { key: 'Korea', value: 'KR' },
    { key: 'Japan', value: 'JP' },
    { key: 'China', value: 'CN' },
  ];

  const fleetDealerData: FleetSelectItem[] = [
    { key: '전체', value: 'ALL' },
    { key: 'Dealer A', value: 'DA' },
    { key: 'Dealer B', value: 'DB' },
    { key: 'Dealer C', value: 'DC' },
  ];

  const typeOption = [
    { key: t('RegionS'), value: 'L' },
    { key: t('CountryS'), value: 'C' },
    { key: t('ModelS'), value: 'M' },
  ];

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    if (field === 'region') {
      setFilterRequest((prev) => ({
        ...prev,
        [field]: value,
        country: 'ALL',
      }));
    } else if (
      (field === 'fleetSeqNo' || field === 'sDealer') &&
      value === 'ALL'
    ) {
      setFilterRequest((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    } else if (field === 'fleetSeqNo') {
      setFilterRequest((prev) => ({
        ...prev,
        [field]: Number(value),
      }));
    } else {
      setFilterRequest((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // 라디오 버튼 선택 상태
  const [periodType] = useState<string>('1');

  // 일별 데이터
  const [selectMonth, setSelectMonth] = useState<Value>(
    new Date(getCurrentYearMonth()),
  );

  // Mock 데이터
  const mockStatEngineRunTime: StatEngineRunTimeData[] = [
    { hours: 120, maxH: 200, ename: 'Region A', days: 15, maxD: 30 },
    { hours: 180, maxH: 200, ename: 'Region B', days: 22, maxD: 30 },
    { hours: 90, maxH: 200, ename: 'Region C', days: 10, maxD: 30 },
  ];

  // 연도별
  const [selectedYear, setSelectedYear] = useState<string>(getCurrentYear());

  // 기간별 데이터
  const [selectPeriodicDate, setSelectPeriodicDate] = useState<{
    sDate: string;
    eDate: string;
  }>({
    sDate: formatDate(new Date()),
    eDate: formatDate(new Date()),
  });

  // 검색 실행 함수
  const handleSearch = async () => {
    console.log('Search filters:', filtersRequest);
    console.log('Period type:', periodType);
    console.log('Selected month:', selectMonth);
    console.log('Selected year:', selectedYear);
    console.log('Selected period:', selectPeriodicDate);
  };

  useEffect(() => {
    handleSearch();
  }, [periodType]);

  const [tableDataState, setTableDataState] = useState<
    EquipmentUptimeTableProps[]
  >([]);

  const { table } = useTable(tableDataState, columns);

  const setEngineWidth = (item: StatEngineRunTimeData): string => {
    if (item.hours && item.maxH) {
      const percent: number = item.hours / item.maxH;
      const width = (560 * percent).toFixed(0);
      return `${width}`;
    }
    return `0`;
  };

  const setActiveWidth = (item: StatEngineRunTimeData): string => {
    if (item.days && item.maxD) {
      const percent: number = item.days / item.maxD;
      const width = (560 * percent).toFixed(0);
      return `${width}`;
    }
    return `0`;
  };

  // tableData를 Mock 데이터로 생성
  const tableData = useMemo((): EquipmentUptimeTableProps[] => {
    return mockStatEngineRunTime.map((item) => ({
      time: item.hours?.toString() || '',
      engineActiveTime: <EngineActive width={setEngineWidth(item)} />,
      region: item.ename || '',
      activeDate: <ActiveDate width={setActiveWidth(item)} />,
      dayCount: item.days?.toString() || '',
    }));
  }, []);

  useEffect(() => {
    setTableDataState(tableData);
  }, [tableData]);

  return (
    <CustomFrame name={t('OperationHour')}>
      <div className={'w-full h-full space-y-10 p-10 overflow-x-hidden'}>
        <div className={'space-y-5'}>
          <SearchItemContainer
            className={'flex-wrap justify-start gap-4 md:gap-6'}
          >
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('Fleet')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('fleetSeqNo', value.toString())
                }
                options={fleetDropData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('Region')}</SearchLabel>
              <DropDown
                options={areaSelectData}
                placeholder={t('All')}
                onChange={(value) =>
                  handleDropdownChange('region', value.toString())
                }
              />
            </SearchItemContainer>
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('Country')}</SearchLabel>
              <DropDown
                options={countrySelectData}
                placeholder={t('All')}
                onChange={(value) =>
                  handleDropdownChange('country', value.toString())
                }
                selectedKey={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('DealerD')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('sDealer', value.toString())
                }
                options={fleetDealerData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('Scope')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('kubun1', value.toString())
                }
                options={typeOption}
                placeholder={t('RegionS')}
                selectedKey={filtersRequest.kubun1}
              />
            </SearchItemContainer>
            <SearchItemContainer className="w-full sm:w-auto">
              <SearchLabel>{t('Model')}</SearchLabel>
              <Input
                placeholder={'Model'}
                className="w-full sm:w-auto"
                onChange={(e) => {
                  handleDropdownChange('smodel', e.target.value);
                }}
                reset={() => {
                  handleDropdownChange('smodel', '');
                }}
              />
            </SearchItemContainer>
          </SearchItemContainer>

          <div className="flex flex-col md:flex-row md:justify-between gap-4">
            <div className="flex flex-col md:flex-row md:justify-between gap-4">
              <Radio
                className="flex flex-col sm:flex-row flex-wrap justify-start gap-4 md:gap-[60px] w-full"
                options={options}
                value={selected}
                onValueChange={setSelected}
              />
              {selected === '0' && (
                <MonthSelector
                  value={selectMonth}
                  onValueChange={(v) => v && setSelectMonth(v)}
                  className="w-full sm:w-[200px]"
                />
              )}
              {selected === '1' && (
                <YearSelector
                  value={selectedYear}
                  onValueChange={(v) =>
                    v && setSelectedYear(dayjs(v as Date).format('YYYY'))
                  }
                  className="w-full sm:w-[200px]"
                />
              )}
              {selected === '2' && (
                <FromToSelector
                  initValue={{
                    start: selectPeriodicDate.sDate,
                    end: selectPeriodicDate.eDate,
                  }}
                  onChange={(s, e) =>
                    setSelectPeriodicDate({
                      sDate: formatDate(new Date(s)),
                      eDate: formatDate(new Date(e)),
                    })
                  }
                />
              )}
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
                className="ml-[-40px]"
              />
            </div>

            <SearchItemContainer className="flex gap-2">
              <Button variant={'bt_primary'} label={'Download'} />
            </SearchItemContainer>
          </div>
        </div>

        <div className={'h-[1087px] overflow-x-auto'}>
          <StatisticsTableWithDiv table={table} columns={columns} />
        </div>
      </div>
    </CustomFrame>
  );
};

export default EquipmentUptime;
