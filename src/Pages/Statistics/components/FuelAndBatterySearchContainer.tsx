import { useTranslation } from 'react-i18next';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { Button } from '@/Common/Components/common/Button';
import { ChangeEvent, useState } from 'react';
import { formatDate, getCurrentYear } from '@/Common/function/date.ts';
import dayjs from 'dayjs';
import YearSelector from '@/Common/Components/datePicker/YearSelector';

interface Filters {
  fleetSeqNo: string;
  region: string;
  country: string;
  sDealer: string;
  model: string;
  hogi: string;
  year: string;
  sDate: string;
  eDate: string;
  periodType: string;
}

interface SearchSelectorProps {
  onValueChange?: (filters: Filters) => void;
}

const FuelAndBatterySearchContainer = (props: SearchSelectorProps) => {
  const { onValueChange } = props;

  const { t } = useTranslation();

  // 검색 필터 상태
  const [filters, setFilters] = useState({
    fleetSeqNo: '',
    region: 'ALL',
    country: 'ALL',
    sDealer: '',
    model: '',
    hogi: '',
    year: getCurrentYear(),
    sDate: formatDate(new Date()),
    eDate: formatDate(new Date()),
    periodType: '1',
  });

  const [selected, setSelected] = useState<string>(filters.periodType);

  // 더미 옵션 데이터
  const fleetDropData = [
    { key: t('All'), value: 'ALL' },
    { key: 'FleetA', value: 'A' },
    { key: 'FleetB', value: 'B' },
  ];

  const areaSelectData = [
    { key: t('All'), value: 'ALL' },
    { key: 'Area1', value: 'Area1' },
    { key: 'Area2', value: 'Area2' },
  ];

  const countrySelectData = [
    { key: t('All'), value: 'ALL' },
    { key: 'KOR', value: 'KOR' },
    { key: 'JPN', value: 'JPN' },
    { key: 'USA', value: 'USA' },
  ];

  const fleetDealerData = [
    { key: t('All'), value: 'ALL' },
    { key: 'DealerA', value: 'DealerA' },
    { key: 'DealerB', value: 'DealerB' },
  ];

  const options: RadioOption[] = [
    { value: '1', label: t('Year') },
    { value: '2', label: t('PeriodD') },
  ];

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    if (field === 'region') {
      setFilters((prev) => ({
        ...prev,
        [field]: value,
        country: 'ALL',
      }));
    } else if (
      (field === 'fleetSeqNo' || field === 'sDealer') &&
      value === 'ALL'
    ) {
      setFilters((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    } else {
      setFilters((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const resetModelInput = () => {
    setFilters((prev) => ({
      ...prev,
      model: '',
    }));
  };

  const resetHogiInput = () => {
    setFilters((prev) => ({
      ...prev,
      hogi: '',
    }));
  };

  // 선택된 월과 연도 상태
  const [selectedYear, setSelectedYear] = useState<string>(getCurrentYear());

  const onYearSelectorChange = (value: Date | Date[] | null) => {
    if (value) {
      const dateValue = Array.isArray(value) ? value[0] : value;
      if (dateValue) {
        setSelectedYear(dayjs(dateValue).format('YYYY'));
        setFilters((prev) => ({
          ...prev,
          year: dayjs(dateValue).format('YYYY'),
          sDate: '',
          eDate: '',
        }));
      }
    }
  };

  // 날짜 기간별
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    if (filters.periodType === '2') {
      const formattedStartDate = formatDate(new Date(startDate));
      const formattedEndDate = formatDate(new Date(endDate));
      setFilters((prev) => ({
        ...prev,
        year: '',
        sDate: formattedStartDate,
        eDate: formattedEndDate,
      }));
    }
  };

  // 라디오 버튼 선택 상태 변경 핸들러
  const handlePeriodTypeChange = (value: string) => {
    setSelected(value);
    setFilters((prev) => ({
      ...prev,
      periodType: value,
      ...(value === '1'
        ? { year: getCurrentYear(), sDate: '', eDate: '' }
        : { year: '' }),
    }));
  };

  const handleSearch = () => {
    if (onValueChange) {
      onValueChange(filters);
    }
  };

  return (
    <div className={'space-y-5'}>
      <SearchItemContainer className={'justify-start gap-6'}>
        <SearchItemContainer>
          <SearchLabel>{t('Fleet')}</SearchLabel>
          <DropDown
            onChange={(value) =>
              handleDropdownChange('fleetSeqNo', value.toString())
            }
            options={fleetDropData}
            placeholder={t('All')}
          />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('Region')}</SearchLabel>
          <DropDown
            options={areaSelectData}
            placeholder={t('All')}
            onChange={(value) =>
              handleDropdownChange('region', value.toString())
            }
          />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('Country')}</SearchLabel>
          <DropDown
            options={countrySelectData}
            placeholder={t('All')}
            onChange={(value) =>
              handleDropdownChange('country', value.toString())
            }
            selectedKey={t('All')}
          />
        </SearchItemContainer>
        <SearchItemContainer>
          <SearchLabel>{t('DealerD')}</SearchLabel>
          <DropDown
            onChange={(value) =>
              handleDropdownChange('sDealer', value.toString())
            }
            options={fleetDealerData}
            placeholder={t('All')}
          />
        </SearchItemContainer>
      </SearchItemContainer>
      <SearchItemContainer className={'justify-between'}>
        <SearchItemContainer>
          <SearchItemContainer className={'justify-start'}>
            <SearchLabel>{t('Model')}</SearchLabel>
            <div className={'w-[200px]'}>
              <Input
                placeholder={t('Model')}
                name="model"
                value={filters.model}
                onChange={handleInputChange}
                reset={resetModelInput}
              />
            </div>
          </SearchItemContainer>
          <SearchItemContainer className={'justify-start'}>
            <SearchLabel>{t('MachineID')}</SearchLabel>
            <div className={'w-[200px]'}>
              <Input
                placeholder={t('MachineID')}
                name="hogi"
                value={filters.hogi}
                onChange={handleInputChange}
                reset={resetHogiInput}
              />
            </div>
          </SearchItemContainer>
        </SearchItemContainer>
      </SearchItemContainer>

      <SearchItemContainer className="justify-between">
        <SearchItemContainer className="justify-between">
          <SearchItemContainer className="gap-5">
            <Radio
              className="flex"
              options={options}
              value={selected}
              onValueChange={handlePeriodTypeChange}
            />

            {selected === '1' && (
              <div className="mr-[60px] flex items-center gap-6">
                <YearSelector
                  value={selectedYear}
                  onValueChange={onYearSelectorChange}
                />
              </div>
            )}

            {selected === '2' && (
              <div className="flex items-center gap-6">
                <FromToSelector
                  onChange={(startDate, endDate) =>
                    handleDateRangeChange(startDate, endDate)
                  }
                />
              </div>
            )}

            <Button
              variant={'bt_primary'}
              label={'aSearchaa'}
              onClick={handleSearch}
            />
          </SearchItemContainer>

          <SearchItemContainer>
            <Button variant={'bt_primary'} label={'Download'} />
          </SearchItemContainer>
        </SearchItemContainer>
      </SearchItemContainer>
    </div>
  );
};

export default FuelAndBatterySearchContainer;
