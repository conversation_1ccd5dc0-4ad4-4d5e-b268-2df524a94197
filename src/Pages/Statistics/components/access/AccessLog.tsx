import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { Button } from '@/Common/Components/common/Button';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CommonTable from '@/Common/Components/common/CommonTable';
import { StatisticsType } from '@/types/StatisticsType';

const AccessLog = () => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.AccessLogTable>[] = [
    {
      accessorKey: 'date',
      header: () => <CustomColumnHeader>{t('Date')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.date}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'id',
      header: () => <CustomColumnHeader>{t('UserId')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.id}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'type',
      header: () => <CustomColumnHeader>{t('Scope')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.type}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'name',
      header: () => <CustomColumnHeader>{t('UserName')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.name}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'dealer',
      header: () => <CustomColumnHeader>{t('Dealership')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.dealer}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'region',
      header: () => <CustomColumnHeader>{t('Region')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.region}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'nation',
      header: () => <CustomColumnHeader>{t('Country')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.nation}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'authority',
      header: () => <CustomColumnHeader>{t('Permission')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.authority}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'ip',
      header: () => <CustomColumnHeader>{t('IP')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.ip}</CustomColumnDataCell>
      ),
    },
  ];

  const data = [
    {
      date: '2024-10-28 18:07',
      id: 'SUPROVAL',
      type: 'Web',
      name: 'SUPROVAL S.L.',
      dealer: '-',
      region: 'Europe',
      nation: 'Spain',
      authority: 'Dealer Manager',
      ip: '************',
    },
    {
      date: '2024-10-28 18:12',
      id: 'SUPROVAL',
      type: 'Web',
      name: 'SUPROVAL S.L.',
      dealer: '-',
      region: 'Europe',
      nation: 'Spain',
      authority: 'Repair Supervisor',
      ip: '*************',
    },
    {
      date: '2024-10-28 18:30',
      id: 'SUPROVAL',
      type: 'Web',
      name: 'SUPROVAL S.L.',
      dealer: '-',
      region: 'Europe',
      nation: 'Spain',
      authority: 'MMC Manager',
      ip: '***********',
    },
    {
      date: '2024-10-28 18:41',
      id: 'SUPROVAL',
      type: 'Web',
      name: 'SUPROVAL S.L.',
      dealer: '-',
      region: 'Europe',
      nation: 'Spain',
      authority: 'MMC Manager',
      ip: '************',
    },
    {
      date: '2024-10-28 18:44',
      id: 'SUPROVAL',
      type: 'Web',
      name: 'SUPROVAL S.L.',
      dealer: '-',
      region: 'Europe',
      nation: 'Spain',
      authority: 'Dealer Manager',
      ip: '************',
    },
  ];

  return (
    <Tabs.Content value={'AccessLog'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <div className={'space-y-5'}>
          <SearchItemContainer className={'justify-start gap-6'}>
            <SearchItemContainer>
              <SearchLabel>{t('Permission')}</SearchLabel>
              <DropDown
                options={[]}
                placeholder={t('Alll')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Categories')}</SearchLabel>
              <DropDown
                options={[]}
                placeholder={t('Alll')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Region')}</SearchLabel>
              <DropDown
                options={[]}
                placeholder={t('Alll')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Country')}</SearchLabel>
              <DropDown
                options={[]}
                placeholder={t('Alll')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel>{t('DealerCode')}</SearchLabel>
              <div className={'w-[200px]'}>
                <Input placeholder={t('DealershipCode')} />
              </div>
            </SearchItemContainer>
          </SearchItemContainer>
          <SearchItemContainer className={'justify-between'}>
            <SearchItemContainer>
              <SearchItemContainer className={'justify-start'}>
                <SearchLabel>{t('UserID')}</SearchLabel>
                <div className={'w-[200px]'}>
                  <Input placeholder={t('UserID')} />
                </div>
              </SearchItemContainer>
              <SearchItemContainer className={'justify-start'}>
                <SearchLabel>{t('UserName')}</SearchLabel>
                <div className={'w-[200px]'}>
                  <Input placeholder={t('UserName')} />
                </div>
              </SearchItemContainer>
              <SearchItemContainer className={'justify-start'}>
                <SearchLabel>{t('Date')}</SearchLabel>
                <FromToSelector />
              </SearchItemContainer>
              <Button
                variant={'bt_primary'}
                label={'Search'}
                className="ml-2"
              />
            </SearchItemContainer>

            <SearchItemContainer>
              <Button variant={'bt_primary'} label={'Download'} />
            </SearchItemContainer>
          </SearchItemContainer>
        </div>
        <div className={'h-[602px]'}>
          <CommonTable<StatisticsType.AccessLogTable>
            data={data}
            isPagination={true}
            columns={columns}
          />
        </div>
        {/*<Pagination*/}
        {/*  totalPage={100}*/}
        {/*  currentPage={currentPage}*/}
        {/*  prevPage={() => {*/}
        {/*    if (currentPage === 1) return;*/}
        {/*    setSearchParams({*/}
        {/*      currentPage: (currentPage - 1).toString(),*/}
        {/*    });*/}
        {/*  }}*/}
        {/*  nextPage={() => {*/}
        {/*    if (currentPage > 99) return;*/}
        {/*    setSearchParams({*/}
        {/*      currentPage: (currentPage + 1).toString(),*/}
        {/*    });*/}
        {/*  }}*/}
        {/*  firstPage={() => {*/}
        {/*    setSearchParams({ currentPage: '1' });*/}
        {/*  }}*/}
        {/*  lastPage={() => {*/}
        {/*    setSearchParams({ currentPage: '100' });*/}
        {/*  }}*/}
        {/*/>*/}
      </div>
    </Tabs.Content>
  );
};

export default AccessLog;
