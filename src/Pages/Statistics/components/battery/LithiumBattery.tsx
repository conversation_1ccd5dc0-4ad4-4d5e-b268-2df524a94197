import batteryWarning from '@/assets/images/battery/battery_warning.svg';
import extinguisher from '@/assets/images/battery/extinguisher.svg';
import dischgarge from '@/assets/images/battery/dischgarge.svg';
import batteryFullWarning from '@/assets/images/battery/battery_full_warning.svg';
import batteryFullCheck from '@/assets/images/battery/battery_full_check.svg';
import batteryEmpty from '@/assets/images/battery/battery_empty.svg';
import batteryI from '@/assets/images/battery/battery_i.svg';
import temperatureWarning from '@/assets/images/battery/temperature_warning.svg';
import { Button } from '@/Common/Components/common/Button';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import useDimension from '@/hooks/useDimension.tsx';
import ChargeAmount from '@/Pages/Statistics/components/battery/ChargeAmount.tsx';
import LithiumStatistics from '@/Pages/Statistics/components/battery/LithiumStatistics.tsx';
import EqWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/EqWorkTimeRanking.tsx';
import StatisticsTable from '@/Pages/Statistics/components/StatisticsTable.tsx';
import { Tabs } from '@radix-ui/themes';
import { ColumnDef } from '@tanstack/react-table';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { StatisticsType } from '@/types/StatisticsType';

export const BatteryWarnings = {
  FIRE_EXTINGUISHER_WARNING: extinguisher,
  CHARGING_CURRENT_WARNING: batteryEmpty,
  PACK_VOLTAGE_WARNING: batteryFullCheck,
  CELL_VOLTAGE_WARNING: dischgarge,
  TEMPERATURE_WARNING: temperatureWarning,
  DISCHARGE_CURRENT_WARNING: dischgarge,
  LOW_SOC_WARNING: batteryI,
  BATTERY_WARNING: batteryI,
  BTMS_WARNING: batteryFullWarning,
  CELL_DEVIATION_WARNING: batteryWarning,
};

export const BatteryWarningsLabel = {
  FIRE_EXTINGUISHER_WARNING: '소화기 경고',
  CHARGING_CURRENT_WARNING: '충전전류 경고',
  PACK_VOLTAGE_WARNING: '팩전압 경고',
  CELL_VOLTAGE_WARNING: '셀전압 경고',
  TEMPERATURE_WARNING: '온도 경고',
  DISCHARGE_CURRENT_WARNING: '방전전류 경고',
  LOW_SOC_WARNING: '배터리 Low SOC 경고',
  BATTERY_WARNING: '배터리 경고',
  BTMS_WARNING: 'BTMS 경고',
  CELL_DEVIATION_WARNING: '셀간 편차 경고',
};

const LithiumBattery = () => {
  const { t } = useTranslation();

  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(4열), md: 태블릿(2열), sm: 작은 태블릿(2열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 2, h: 10.3, static: false },
      { i: 'b', x: 2, y: 0, w: 2, h: 10.3, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 20.5, static: false },
      { i: 'd', x: 2, y: 1, w: 2, h: 20.5, static: false },
      { i: 'e', x: 0, y: 2, w: 2, h: 12.5, static: false },
      { i: 'f', x: 2, y: 2, w: 2, h: 12.5, static: false },
      { i: 'g', x: 0, y: 2, w: 2, h: 10.3, static: false },
      { i: 'h', x: 2, y: 2, w: 2, h: 10.3, static: false },
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 2, h: 10.3, static: false },
      { i: 'b', x: 2, y: 0, w: 2, h: 10.3, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 20.5, static: false },
      { i: 'd', x: 2, y: 1, w: 2, h: 20.5, static: false },
      { i: 'e', x: 0, y: 2, w: 2, h: 12.5, static: false },
      { i: 'f', x: 2, y: 2, w: 2, h: 12.5, static: false },
      { i: 'g', x: 0, y: 2, w: 2, h: 10.3, static: false },
      { i: 'h', x: 2, y: 2, w: 2, h: 10.3, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 2, h: 10.3, static: false },
      { i: 'b', x: 2, y: 0, w: 2, h: 10.3, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 20.5, static: false },
      { i: 'd', x: 2, y: 1, w: 2, h: 20.5, static: false },
      { i: 'e', x: 0, y: 2, w: 2, h: 12.5, static: false },
      { i: 'f', x: 2, y: 2, w: 2, h: 12.5, static: false },
      { i: 'g', x: 0, y: 2, w: 2, h: 10.3, static: false },
      { i: 'h', x: 2, y: 2, w: 2, h: 10.3, static: false },
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 2, h: 10.3, static: false },
      { i: 'b', x: 2, y: 0, w: 2, h: 10.3, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 20.5, static: false },
      { i: 'd', x: 2, y: 1, w: 2, h: 20.5, static: false },
      { i: 'e', x: 0, y: 2, w: 2, h: 12.5, static: false },
      { i: 'f', x: 2, y: 2, w: 2, h: 12.5, static: false },
      { i: 'g', x: 0, y: 2, w: 2, h: 10.3, static: false },
      { i: 'h', x: 2, y: 2, w: 2, h: 10.3, static: false },
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 4, md: 2, sm: 2, xs: 1 };

  const { width } = useDimension();
  const gridWidth = width > 1024 ? width - 280 : width - 72;

  const [searchParams, setSearchParams] = useSearchParams({ currentPage: '1' });
  const currentPage = Number(searchParams.get('currentPage'));
  const columns: ColumnDef<StatisticsType.LithiumBatteryTable>[] = [
    {
      accessorKey: 'model',
      header: () => <CustomColumnHeader>{t('Model')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.model}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'eq',
      header: () => <CustomColumnHeader>{t('MachineID')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.eq}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'no',
      header: () => <CustomColumnHeader>{t('SerialNo')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.no}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'mileage',
      header: () => <CustomColumnHeader>{t('Mileage')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.mileage}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'sendDate',
      header: () => (
        <CustomColumnHeader>{t('DeliveryDateE')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.sendDate}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'warranty',
      header: () => (
        <CustomColumnHeader>{t('WarrentyExpirationDate')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.warranty}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'alarm',
      header: () => <CustomColumnHeader>{t('Alarm')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          <div className="w-9 h-9 bg-white rounded-lg border border-[#e6e6e6] flex justify-center items-center items-center">
            <img src={row.original.alarm[0]} alt={''} />
          </div>
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'errorType',
      header: () => <CustomColumnHeader>{t('TypeE')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.errorType}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'errorLevel',
      header: () => <CustomColumnHeader>{t('ServerityE')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.errorLevel}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'errorDate',
      header: () => <CustomColumnHeader>{t('DateTimeE')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.errorDate}</CustomColumnDataCell>
      ),
    },
  ];
  const { table } = useTable(
    [
      {
        model: '50B-9',
        eq: '0085',
        no: 'BSNA0085',
        mileage: '1417',
        sendDate: '2024-11-12',
        warranty: '2025-11-12',
        alarm: [
          BatteryWarnings.BTMS_WARNING,
          BatteryWarningsLabel.BTMS_WARNING,
        ],
        errorType: 'BTMS Warning',
        errorLevel: '2',
        errorDate: '2024-12-11 11:40',
      },
      {
        model: '50B-9',
        eq: '0084',
        no: 'BSNA0084',
        mileage: '1427',
        sendDate: '2024-11-12',
        warranty: '2025-11-12',
        alarm: [
          BatteryWarnings.BATTERY_WARNING,
          BatteryWarningsLabel.BATTERY_WARNING,
        ],
        errorType: 'Battery Warning',
        errorLevel: '3',
        errorDate: '2024-12-21 14:10',
      },
      {
        model: '50B-9',
        eq: '0083',
        no: 'BSNA0083',
        mileage: '1306',
        sendDate: '2024-11-12',
        warranty: '2025-11-12',
        alarm: [
          BatteryWarnings.DISCHARGE_CURRENT_WARNING,
          BatteryWarningsLabel.DISCHARGE_CURRENT_WARNING,
        ],
        errorType: 'Discharge Current Warning',
        errorLevel: '2',
        errorDate: '2024-12-29 16:29',
      },
      {
        model: '50B-9',
        eq: '0082',
        no: 'BSNA0082',
        mileage: '1230',
        sendDate: '2024-11-12',
        warranty: '2025-11-12',
        alarm: [
          BatteryWarnings.PACK_VOLTAGE_WARNING,
          BatteryWarningsLabel.PACK_VOLTAGE_WARNING,
        ],
        errorType: 'Pack Voltage Warning',
        errorLevel: '3',
        errorDate: '2024-12-31 11:29',
      },
    ],
    columns,
  );

  const wrRows = [
    {
      name: 'HXDEMO',
      number: '0819',
      where: 'US-01',
      hitNumber: 0,
      time: '219 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-01',
      number: '0820',
      hitNumber: 0,
      time: '211 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-02',
      number: '0828',
      hitNumber: 0,
      time: '208 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-03',
      number: '0830',
      hitNumber: 0,
      time: '105 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-02',
      number: '0862',
      hitNumber: 0,
      time: '98 Hours',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-03',
      number: '0882',
      hitNumber: 0,
      time: '96 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-01',
      number: '0900',
      hitNumber: 0,
      time: '96 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-02',
      number: '0990',
      hitNumber: 0,
      time: '95 Hours',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-03',
      number: '0992',
      hitNumber: 0,
      time: '91 Hours',
      upDown: false,
    },
  ];

  const sdRows = [
    {
      name: 'HXDEMO',
      number: '0819',
      where: 'US-01',
      hitNumber: 0,
      time: '23.57%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-01',
      number: '0820',
      hitNumber: 0,
      time: '21.08%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-02',
      number: '0828',
      hitNumber: 0,
      time: '20.94%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'GJ-03',
      number: '0830',
      hitNumber: 0,
      time: '15.64%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-02',
      number: '0862',
      hitNumber: 0,
      time: '13.42%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      where: 'US-03',
      number: '0882',
      hitNumber: 0,
      time: '16.47%',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-01',
      number: '0900',
      hitNumber: 0,
      time: '13.49%',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-02',
      number: '0990',
      hitNumber: 0,
      time: '20.30%',
      upDown: false,
    },
    {
      name: 'HXDEMO',
      where: 'CU-03',
      number: '0992',
      hitNumber: 0,
      time: '13.42%',
      upDown: false,
    },
  ];

  return (
    <Tabs.Content value={'LithiumBattery'}>
      <div className={'space-y-5 pt-10 pb-3'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer>
            <SearchLabel>{t('Fleet')}</SearchLabel>
            <DropDown options={[]} placeholder={t('All')} onChange={() => {}} />
          </SearchItemContainer>
          <Button variant={'bt_primary'} label={'Download'} />
        </SearchItemContainer>
      </div>
      <ResponsiveGridLayout
        className="layout px-4"
        layouts={layouts}
        breakpoints={breakpoints}
        cols={cols}
        isDroppable={false}
        isDraggable={false}
        rowHeight={1}
        width={gridWidth}
        margin={[16, 16]}
        containerPadding={[16, 16]}
      >
        <div className={'p-2'} key={'a'}>
          <LithiumStatistics
            title={t('TotalChangingCount')}
            value={'300'}
            unit={t('Times')}
          />
        </div>

        <div className={'p-2'} key={'b'}>
          <LithiumStatistics title={t('AverageSOH')} value={'68'} unit={'%'} />
        </div>

        <div className={'p-2'} key={'c'}>
          <EqWorkTimeRanking eq={wrRows} />
        </div>

        <div className={'p-2'} key={'d'}>
          {/*<ExpendableStatistics />*/}
          <EqWorkTimeRanking eq={sdRows} title={t('SOHDegradation')} />
        </div>
        <div className={'p-2'} key={'e'}>
          <ChargeAmount
            title={t('AccumulatedChargingCapacity')}
            value={'2,324,564'}
            unit={'Ah'}
            up={true}
          />
        </div>
        <div className={'p-2'} key={'f'}>
          <ChargeAmount
            title={t('AccumulatedDischargingCapacity')}
            value={'1,456,344'}
            unit={'Ah'}
            up={false}
          />
        </div>
        <div className={'p-2'} key={'g'}>
          <LithiumStatistics title={t('NumberOfCharges')} value={'323'} />
        </div>
        <div className={'p-2'} key={'h'}>
          <LithiumStatistics
            title={t('NumberOfOverDischarging')}
            value={'73'}
          />
        </div>
      </ResponsiveGridLayout>
      <div className={'p-10'}>
        <div className="w-full h-0.5 bg-[#d9d9d9]" />
      </div>
      <div className={'space-y-8 pb-10'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchLabel>{t('WarningLightActivationList')}</SearchLabel>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer className={'gap-6'}>
            <SearchItemContainer>
              <SearchLabel>{t('Model')}</SearchLabel>
              <Input placeholder={t('Model')} className="w-[119px]" />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('MachineID')}</SearchLabel>
              <Input placeholder={t('MachineID')} className="w-[105px]" />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('TypeE')}</SearchLabel>
              <DropDown
                options={[]}
                onChange={() => {}}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Severity')}</SearchLabel>
              <DropDown
                options={[]}
                onChange={() => {}}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Date')}</SearchLabel>
              <FromToSelector />
            </SearchItemContainer>
          </SearchItemContainer>

          <Button variant={'bt_primary'} label={'Search'} />
        </SearchItemContainer>
        <StatisticsTable table={table} columns={columns} />
      </div>
    </Tabs.Content>
  );
};

export default LithiumBattery;
