import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Editor } from '@tinymce/tinymce-react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { useToast } from '@/Common/useToast.tsx';
import { Button } from '@/Common/Components/common/Button';
import Checkbox from '@/Common/Components/common/CheckBox';
import TempSavePopup from '@/Pages/Notice/components/TempSavePopup.tsx';
import SearchItemContainer from '@/Common/Components/etc/SearchItemContainer';
import SearchLabel from '@/Common/Components/etc/SearchLabel';
import Radio from '@/Common/Components/common/Radio';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import TimeSelector from '@/Common/Components/datePicker/TimeSelector';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import FileDropDown from '@/Common/Components/common/FileDropDown';
import useNoticePopup from '@/Pages/Notice/components/useNoticePopup.tsx';

const NoticeRegistration = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [noticeType, setNoticeType] = useState('NORMAL');
  const [readPermission, setReadPermission] = useState('ALL');
  const [langType, setLangType] = useState('KR');
  const [topFixed, setTopFixed] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);

  const { openPageOutPopup } = useNoticePopup();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');

  const [periodType, setPeriodType] = useState('1');

  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('09:00');
  const [endDate, setEndDate] = useState('');
  const [endTime, setEndTime] = useState('18:00');
  const [existingFileNames, setExistingFileNames] = useState<string[]>([]);

  const { noticeId } = useParams();
  const isEdit = !!noticeId;

  const [selected, setSelected] = useState('0');
  const options = [
    { value: '0', label: t('AlwaysVisible') },
    { value: '1', label: t('ScheduledPost') },
  ];

  const handleFileChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  const handleLoadTempItem = (item: {
    title?: string;
    context?: string;
    noticeType?: string;
  }) => {
    setTitle(item.title ?? '');
    setContent(item.context ?? '');
    setNoticeType(
      ['SYSTEM', 'TERMS', 'UPDATE', 'NORMAL'].includes(item.noticeType ?? '')
        ? (item.noticeType ?? 'NORMAL')
        : 'NORMAL',
    );
  };

  const handleClickTempSavePopup = () => {
    setIsTempPopupOpen(true);
  };

  const noticeTypeOptions = [
    { key: t('SystemCheck'), value: 'SYSTEM' },
    { key: t('TermsChanges'), value: 'TERMS' },
    { key: t('Update'), value: 'UPDATE' },
    { key: t('Normal'), value: 'NORMAL' },
  ];

  const readPermissionOptions = [
    { key: t('All'), value: 'ALL' },
    { key: t('Dealer'), value: 'DEALER' },
  ];

  const langTypeOptions = [
    { key: t('Korean'), value: 'KR' },
    { key: t('English'), value: 'US' },
  ];

  const handleRegister = () => {
    if (!title.trim() || !content.trim()) return;
    navigate('/notice');
  };

  const isPageOut = () => {
    openPageOutPopup(() => {
      navigate(-1);
    });
  };

  useEffect(() => {
    if (isEdit) {
      // 필요시 setTitle, setContent 등 기존 데이터로 세팅
    }
  }, [isEdit]);

  const handleTemporarySave = () => {
    if (!title.trim() && !content.trim()) return;
    toast({
      types: 'success',
      description: t('Savedtemporarily'),
    });
  };

  const handleRemoveExistingFile = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  return (
    <CustomFrame
      name={isEdit ? t('NoticeModify') : t('NoticeRegistration')}
      back={true}
      onBackClick={isPageOut}
    >
      <section className="w-b-b-r-30">
        {/*  */}
        <article className="mb-3 f-c-b gap-5">
          <div className="f-c-b gap-5">
            <SearchLabel>{t('DisplayPeriod')}</SearchLabel>
            <Radio
              options={options}
              value={selected}
              onValueChange={setSelected}
            />
            <div
              style={{
                opacity: periodType === '1' ? 0.5 : 1,
                pointerEvents: periodType === '1' ? 'none' : 'auto',
              }}
            >
              <SearchLabel>{t('PostingTime')}</SearchLabel>
              <SearchItemContainer
                style={{
                  opacity: periodType === '1' ? 0.5 : 1,
                  pointerEvents: periodType === '1' ? 'none' : 'auto',
                }}
              >
                <DaySelector initValue={startDate} onChange={setStartDate} />
                <TimeSelector initValue={startTime} onChange={setStartTime} />
                <span>~</span>
                <DaySelector initValue={endDate} onChange={setEndDate} />
                <TimeSelector initValue={endTime} onChange={setEndTime} />
              </SearchItemContainer>
            </div>
          </div>
          <div className="f-c gap-[10px]">
            <Button
              variant={'bt_tertiary'}
              label={'LoadDraft'}
              onClick={handleClickTempSavePopup}
            />
            <Button
              variant={'bt_secondary'}
              label={'SaveAsDraft'}
              disabled={!title.trim() && !content.trim()}
              onClick={handleTemporarySave}
            />
            <Button
              variant={'bt_primary'}
              label={'Register'}
              disabled={!title.trim() || !content.trim()}
              onClick={handleRegister}
            />
          </div>
        </article>

        {/*  */}
        <article className="pb-10 f-c gap-5 border-b border-gray-6">
          <SearchItemContainer>
            <SearchLabel>{t('NoticeType')}</SearchLabel>
            <DropDown
              placeholder={t('SelectNoticeType')}
              options={noticeTypeOptions}
              selectedKey={noticeType}
              onChange={(value) => setNoticeType(value as string)}
            />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('ViewPermission')}</SearchLabel>
            <DropDown
              placeholder={t('All')}
              options={readPermissionOptions}
              selectedKey={readPermission}
              onChange={(value) => setReadPermission(value as string)}
            />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('Language')}</SearchLabel>
            <DropDown
              placeholder={t('All')}
              options={langTypeOptions}
              selectedKey={langType}
              onChange={() => {}}
            />
          </SearchItemContainer>
          <Checkbox
            label={t('PinnedToTop')}
            checked={topFixed}
            onCheckedChange={(checked) => setTopFixed(!!checked)}
          />
        </article>

        <article className="mt-10 space-y-[30px] [&_p]:w-[100px]">
          <SearchItemContainer className="gap-5">
            <SearchLabel>
              <p>
                {t('Title')}
                <span className="ml-1 text-semantic-4">*</span>
              </p>
            </SearchLabel>
            <Input
              placeholder={t('Title')}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </SearchItemContainer>
          <SearchItemContainer
            style={{ alignItems: 'flex-start' }}
            className="gap-5"
          >
            <SearchLabel>
              <p>
                {t('Content')}
                <span className="ml-1 text-semantic-4">*</span>
              </p>
            </SearchLabel>
            <Editor
              value={content}
              onEditorChange={(newContent) => setContent(newContent)}
              apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'anchor',
                  'autolink',
                  'charmap',
                  'codesample',
                  'link',
                  'lists',
                  'searchreplace',
                  'visualblocks',
                  'wordcount',
                ],
                toolbar:
                  'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                ai_request: () => {},
              }}
              initialValue=""
            />
          </SearchItemContainer>
          <SearchItemContainer className="gap-5">
            <SearchLabel>
              <p>{t('AttatchFile')}</p>
            </SearchLabel>
            <FileDropDown
              onFilesChange={handleFileChange}
              existingFileNames={existingFileNames}
              onRemoveExistingFileName={handleRemoveExistingFile}
            />
          </SearchItemContainer>
        </article>

        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </section>
    </CustomFrame>
  );
};

export default NoticeRegistration;
