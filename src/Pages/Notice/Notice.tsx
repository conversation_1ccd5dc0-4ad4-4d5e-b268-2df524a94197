import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/Common/useToast.tsx';
import { CustomFrame } from '@/Pages/CustomFrame';
import UseNoticePopup from '@/Pages/Notice/components/useNoticePopup';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import Badge from '@/Common/Components/common/Badge';

const Notice = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { openNoticeDeletePopup } = UseNoticePopup();

  // State: 검색, 페이징, 선택 항목
  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  const [titleKeyword, setTitleKeyword] = useState('');
  const [searchTitleKeyword, setSearchTitleKeyword] = useState('');

  const [tempNoticeType, setTempNoticeType] = useState('ALL');
  const [searchNoticeType, setSearchNoticeType] = useState('ALL');

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);
  const [tableKey, setTableKey] = useState(0);

  // Dropdown 옵션 (enum 없이 value만 문자열)
  const [noticeTypeOptions] = useState([
    { key: t('AllNotice'), value: 'all' },
    { key: t('SystemMaintenance'), value: 'system' },
    { key: t('TermsUpdate'), value: 'terms' },
    { key: t('GeneralUpdate'), value: 'general' },
  ]);

  // 더미 Notice 데이터
  const [noticeListData, setNoticeListData] = useState([
    {
      no: '1',
      type: 'System',
      title: '서비스 점검 안내',
      author: 'admin',
      viewCount: '18',
      regDt: '2024-07-15',
      isNew: true,
      isTopFixed: true,
      check: false,
    },
    {
      no: '2',
      type: 'Update',
      title: '기능 업데이트 공지',
      author: '홍길동',
      viewCount: '4',
      regDt: '2024-07-12',
      isNew: false,
      isTopFixed: false,
      check: false,
    },
  ]);

  // 검색
  const handleSearch = () => {
    setSearchTitleKeyword(titleKeyword);
    setSearchNoticeType(tempNoticeType);
    setPageNum(1);
    // 실제로는 필터링 로직 필요
  };

  // 삭제 핸들러 (더미)
  const handleDeleteNotices = (close: () => void) => {
    toast({ types: 'warning', description: t('NoticeDeleted') });
    close();
    setSelectedCheck([]);
    setTableKey((prev) => prev + 1);
  };

  // 테이블 컬럼 정의 (ColumnDef, 타입 없이 객체 구조)
  const columns = [
    {
      accessorKey: 'no',
      size: 80,
      header: t('No'),
      cell: ({
        row,
      }: {
        row: { original: (typeof noticeListData)[number] };
      }) => {
        const data = row.original;
        return (
          <div>
            {data.isTopFixed ? <Badge message={t('NoticeBadge')} /> : data.no}
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      size: 80,
      header: t('NoticeType'),
      cell: ({
        row,
      }: {
        row: { original: (typeof noticeListData)[number] };
      }) => <div>{row.original.type}</div>,
    },
    {
      accessorKey: 'title',
      size: 800,
      header: t('Title'),
      cell: ({
        row,
      }: {
        row: { original: (typeof noticeListData)[number] };
      }) => (
        <div className="f-c gap-2">
          {row.original.isNew && <Badge message={t('New')} />}
          {row.original.title}
        </div>
      ),
    },
    {
      accessorKey: 'author',
      size: 80,
      header: t('Author'),
    },
    {
      accessorKey: 'viewCount',
      size: 80,
      header: t('Views'),
      cell: ({
        row,
      }: {
        row: { original: (typeof noticeListData)[number] };
      }) => (
        <div>{row.original.viewCount === '-' ? 0 : row.original.viewCount}</div>
      ),
    },
    {
      accessorKey: 'regDt',
      size: 80,
      header: t('PostedDate'),
    },
  ];

  return (
    <CustomFrame name={t('Notice')}>
      <section className="w-b-b-r-30">
        <div className="f-c gap-4">
          <div className="f-c gap-[10px]">
            <DropDown
              onChange={(value) => setTempNoticeType(value.toString())}
              options={noticeTypeOptions}
              placeholder={t('AllNotice')}
            />
            <Input
              placeholder={t('TitleOrContent')}
              value={titleKeyword}
              onChange={(e) => setTitleKeyword(e.target.value)}
            />
          </div>
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={handleSearch}
          />
        </div>

        <article>
          <div className="mt-[18px] mb-[10px] f-c-e gap-2">
            <Button
              variant={'bt_primary_sm'}
              label={'Delete'}
              onClick={() => openNoticeDeletePopup(handleDeleteNotices)}
              disabled={selectedCheck.length === 0}
            />
            <Button
              variant={'bt_primary_sm'}
              label={'Register'}
              onClick={() => navigate('/notice-add')}
            />
          </div>
          <CommonTable
            key={tableKey}
            columns={columns}
            data={noticeListData}
            isPagination
            isCheckbox
            currentPage={pageNum}
            customPageSize={pageSize}
            totalCount={totalCount}
            onPageChange={setPageNum}
            onSelectionChange={(selected) =>
              setSelectedCheck(selected.map((row) => row.no))
            }
            onRowClick={(row) => navigate(`/notice-detail/${row.no}`)}
            tdclassName="cursor-pointer"
          />
        </article>
      </section>
    </CustomFrame>
  );
};

export default Notice;
