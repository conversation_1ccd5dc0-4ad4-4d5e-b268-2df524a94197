import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useToast } from '@/Common/useToast';
import { useState, useEffect } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame';
import UseNoticePopup from '@/Pages/Notice/components/useNoticePopup';
import { Button } from '@/Common/Components/common/Button';

const NoticeDetail = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { openNoticeDeletePopup } = UseNoticePopup();
  const { noticeId } = useParams();

  // 더미 공지 데이터
  const [detail, setDetail] = useState({
    title: '샘플 공지사항 제목',
    noticeType: 'System',
    userId: 'admin',
    regDt: '2024-07-15 12:12:00',
    context: '<p>여기는 공지 내용입니다.</p>',
    fileName: ['sample.png'],
  });

  // 더미 noticeList (prev/next 찾기용)
  const [noticeList] = useState([
    {
      noticeId: 1,
      title: '이전 공지사항',
      userId: '홍길동',
      regDt: '2024-07-10',
    },
    {
      noticeId: 2,
      title: '샘플 공지사항 제목',
      userId: 'admin',
      regDt: '2024-07-15',
    },
    {
      noticeId: 3,
      title: '다음 공지사항',
      userId: 'James',
      regDt: '2024-07-20',
    },
  ]);

  // 현재 인덱스, 이전/다음글
  const currentIndex = noticeList.findIndex(
    (n) => String(n.noticeId) === String(noticeId),
  );
  const prevNotice = currentIndex > 0 ? noticeList[currentIndex - 1] : null;
  const nextNotice =
    currentIndex !== -1 && currentIndex < noticeList.length - 1
      ? noticeList[currentIndex + 1]
      : null;

  // 이미지 미리보기 (더미)
  const [imagePreviews, setImagePreviews] = useState<{
    [fileName: string]: string;
  }>({
    sample: 'https://dummyimage.com/400x200/cccccc/000000.png&text=sample',
  });

  // 삭제 핸들러 (더미)
  const handleDeleteDetail = (close: () => void) => {
    toast({ types: 'warning', description: t('NoticeDeleted') });
    close();
    navigate('/notice');
  };

  useEffect(() => {
    if (detail.fileName && detail.fileName.length > 0) {
      detail.fileName.forEach((fileName) => {
        const isImage = /\.(jpg|jpeg|bmp|png|gif)$/i.test(fileName);
        if (isImage && !imagePreviews[fileName]) {
          setImagePreviews((prev) => ({
            ...prev,
            [fileName]:
              'https://dummyimage.com/400x200/cccccc/000000.png&text=' +
              fileName,
          }));
        }
      });
    }
    // eslint-disable-next-line
  }, [noticeId, detail]);

  return (
    <CustomFrame name={t('Notice')} back={true}>
      <article className="w-b-b-r-30">
        {/* Header */}
        <div className="w-full pb-[30px] f-s-b border-b border-gray-6">
          <div>
            <div className="subtitle2">{detail.title}</div>
            <div className="mt-[10px] text-divider [&>div]:border-gray-6 [&_h3]:subtitle5 [&_p]:body3 [&_p]:text-gray-10">
              <div>
                <h3>{t('TypeNotice')}</h3>
                <p>{detail.noticeType}</p>
              </div>
              <div>
                <h3>{t('RegisterNotice')}</h3>
                <p>{detail.userId}</p>
              </div>
              <div>
                <h3>{t('DateRes')}</h3>
                <p>{(detail.regDt ?? '').split(' ')[0]}</p>
              </div>
            </div>
          </div>
          <div className="f-c gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Delete'}
              onClick={() => openNoticeDeletePopup(handleDeleteDetail)}
            />
            <Button
              variant={'bt_primary'}
              label={'Edit'}
              onClick={() => {
                if (noticeId) navigate(`/notice-edit/${noticeId}`);
              }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="pt-10">
          <div
            dangerouslySetInnerHTML={{ __html: detail.context ?? '' }}
            className="mb-10 body2"
          />
          <div>
            {detail.fileName &&
              detail.fileName.map((fileName) => {
                const isImage = /\.(jpg|jpeg|bmp|png|gif)$/i.test(fileName);
                return isImage && imagePreviews[fileName] ? (
                  <div key={fileName}>
                    <img
                      src={imagePreviews[fileName]}
                      alt={fileName}
                      className="object-cover"
                    />
                  </div>
                ) : null;
              })}
          </div>
        </div>

        {/* 이전 글 / 다음 글 */}
        <div className="mt-20">
          {/* 이전 글 영역 */}
          <div
            onClick={
              prevNotice
                ? () => navigate(`/notice-detail/${prevNotice.noticeId}`)
                : undefined
            }
            className="py-5 px-[30px] f-c-b border-y border-gray-6"
          >
            <div className="f-c gap-[50px]">
              <h3 className="w-[75px] subtitle5">{t('Previous')}</h3>
              <p
                style={{
                  color: prevNotice ? undefined : '#cccccc',
                }}
                className="body3"
              >
                {prevNotice ? prevNotice.title : t('ThereIsNoPreviousArticle')}
              </p>
            </div>
            <div className="f-c gap-[30px] body2 text-right">
              <span>{prevNotice ? prevNotice.userId : '-'}</span>
              <span className="w-5">
                {prevNotice ? prevNotice.noticeId : '-'}
              </span>
              <span className="w-[100px]">
                {prevNotice ? prevNotice.regDt : '-'}
              </span>
            </div>
          </div>

          {/* 다음 글 영역 */}
          <div
            onClick={
              nextNotice
                ? () => navigate(`/notice-detail/${nextNotice.noticeId}`)
                : undefined
            }
            className="py-5 px-[30px] f-c-b border-b border-gray-6"
          >
            <div className="f-c gap-[50px]">
              <h3 className="w-[75px] subtitle5">{t('NextT')}</h3>
              <p
                style={{
                  color: nextNotice ? undefined : '#cccccc',
                }}
                className="body3"
              >
                {nextNotice ? nextNotice.title : t('ThereIsNoNextArticle')}
              </p>
            </div>
            <div className="f-c gap-[30px] body2 text-right">
              <span>{nextNotice ? nextNotice.userId : '-'}</span>
              <span className="w-5">
                {nextNotice ? nextNotice.noticeId : '-'}
              </span>
              <span className="w-[100px]">
                {nextNotice ? nextNotice.regDt : '-'}
              </span>
            </div>
          </div>
        </div>
      </article>
    </CustomFrame>
  );
};

export default NoticeDetail;
