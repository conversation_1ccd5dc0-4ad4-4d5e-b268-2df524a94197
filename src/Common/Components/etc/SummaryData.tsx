import React from 'react';

interface SummaryDataProps {
  details: {
    label: string;
    value?: string | number | React.ReactNode | null;
  }[];
  gridCols?: number;
  fs?: 'lg' | 'sm';
}

const SummaryData: React.FC<SummaryDataProps> = ({ details, gridCols, fs }) => {
  const fontSizeClass = fs === 'lg' ? 'subtitle4' : 'subtitle5';

  const containerClass = gridCols
    ? `${fontSizeClass} max-w-[1000px] grid grid-cols-${gridCols} gap-4`
    : `${fontSizeClass} max-w-[1000px] flex flex-wrap gap-3`;

  return (
    <div className={containerClass}>
      {details.map((detail, index) => (
        <div key={index} className="col-span-1">
          <span className="mr-3 text-gray-8">{detail.label}</span>
          <span className="body2">{detail.value ?? '-'}</span>
        </div>
      ))}
    </div>
  );
};

export default SummaryData;
